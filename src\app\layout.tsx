import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme-context";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://ghazal-erfani-portfolio.vercel.app'),
  title: "<PERSON><PERSON><PERSON>'s Portfolio",
  description: "IT Operations student at Red River College seeking entry-level positions in Help Desk Support, System Administration, and Technical Support. Passionate about solving technical problems and helping users.",
  openGraph: {
    title: "<PERSON><PERSON><PERSON>'s Portfolio",
    description: "IT Operations student at Red River College seeking entry-level positions in Help Desk Support, System Administration, and Technical Support. Passionate about solving technical problems and helping users.",
    images: [
      {
        url: "/meta.png",
        width: 1788,
        height: 1370,
        alt: "<PERSON><PERSON><PERSON>'s Portfolio"
      }
    ],
    type: 'website',
    siteName: "<PERSON><PERSON><PERSON>'s Portfolio"
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON><PERSON><PERSON>'s Portfolio",
    description: "IT Operations student at Red River College seeking entry-level positions in Help Desk Support, System Administration, and Technical Support. Passionate about solving technical problems and helping users.",
    images: ["/meta.png"],
    creator: "@GhazalErfani"
  },
  other: {
    'share-id': 'portfolio-ghazal-erfani'
  }
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
