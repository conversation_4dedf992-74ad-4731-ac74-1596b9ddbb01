<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Headshot Image Processor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
        }
        .preview-container {
            display: flex;
            gap: 30px;
            margin: 30px 0;
            justify-content: center;
            align-items: center;
        }
        .preview {
            text-align: center;
        }
        .preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        .circular-preview {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            overflow: hidden;
            border: 4px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: radial-gradient(circle, transparent 60%, rgba(255, 255, 255, 0.1) 100%);
        }
        .circular-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            filter: contrast(1.1) saturate(1.2) brightness(1.05);
            border-radius: 0;
            box-shadow: none;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Headshot Image Processor</h1>
        <p>Upload your headshot image and see how it will look in the circular frame with background optimization.</p>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <div class="step">1. Click the upload area below to select your headshot image</div>
            <div class="step">2. Preview how it looks in both original and circular formats</div>
            <div class="step">3. Download the processed circular version</div>
            <div class="step">4. Replace the file at <code>public/ghazal-headshot.png</code> with your processed image</div>
        </div>

        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📁 Click to Upload Your Headshot</h3>
            <p>Supports JPG, PNG, and WebP formats</p>
            <input type="file" id="fileInput" accept="image/*" onchange="handleFileUpload(event)">
        </div>

        <div class="preview-container" id="previewContainer" style="display: none;">
            <div class="preview">
                <h4>Original Image</h4>
                <img id="originalPreview" alt="Original">
            </div>
            <div class="preview">
                <h4>Circular Preview</h4>
                <div class="circular-preview">
                    <img id="circularPreview" alt="Circular">
                </div>
            </div>
        </div>

        <div style="text-align: center;">
            <button id="downloadBtn" onclick="downloadProcessedImage()" disabled>
                💾 Download Circular Version
            </button>
            <button onclick="resetUpload()">
                🔄 Upload Different Image
            </button>
        </div>
    </div>

    <script>
        let currentImage = null;

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                currentImage = e.target.result;
                
                // Show previews
                document.getElementById('originalPreview').src = currentImage;
                document.getElementById('circularPreview').src = currentImage;
                document.getElementById('previewContainer').style.display = 'flex';
                document.getElementById('downloadBtn').disabled = false;
            };
            reader.readAsDataURL(file);
        }

        function downloadProcessedImage() {
            if (!currentImage) return;

            // Create canvas to process the image
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                // Set canvas size to 192x192 (matching the component)
                canvas.width = 192;
                canvas.height = 192;

                // Create circular clipping path
                ctx.beginPath();
                ctx.arc(96, 96, 96, 0, Math.PI * 2);
                ctx.clip();

                // Calculate dimensions to maintain aspect ratio
                const size = Math.min(img.width, img.height);
                const x = (img.width - size) / 2;
                const y = (img.height - size) / 2;

                // Draw the image
                ctx.drawImage(img, x, y, size, size, 0, 0, 192, 192);

                // Apply filters (simulating CSS filters)
                ctx.filter = 'contrast(1.1) saturate(1.2) brightness(1.05)';

                // Download the processed image
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'ghazal-headshot.png';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };

            img.src = currentImage;
        }

        function resetUpload() {
            document.getElementById('fileInput').value = '';
            document.getElementById('previewContainer').style.display = 'none';
            document.getElementById('downloadBtn').disabled = true;
            currentImage = null;
        }
    </script>
</body>
</html>
