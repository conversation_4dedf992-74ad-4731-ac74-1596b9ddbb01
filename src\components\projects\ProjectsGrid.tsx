'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ExternalLink, Github } from 'lucide-react';

const projects = [
  {
    id: 'help-desk-ticketing',
    title: 'Help Desk Ticketing System Setup',
    category: 'service-management',
    problem: 'IT support requests were being handled through email, causing delays and lost tickets',
    solution: 'Implemented and configured osTicket system with automated routing and SLA tracking',
    result: 'Reduced average response time by 65% and improved ticket tracking accuracy',
    tags: ['osTicket', 'ITIL', 'Service Management', 'MySQL'],
    emoji: '🎫',
    color: 'from-blue-500 to-cyan-500',
    github: 'https://github.com/ghazalerfani/help-desk-setup',
    demo: '',
    difficulty: 'Intermediate',
    timeSpent: '2 months',
    impact: 'High'
  },
  {
    id: 'active-directory-lab',
    title: 'Active Directory Lab Environment',
    category: 'system-administration',
    problem: 'Need for hands-on practice with enterprise directory services',
    solution: 'Built complete AD environment with domain controllers, GPOs, and user management',
    result: 'Gained practical experience with enterprise identity management',
    tags: ['Active Directory', 'Windows Server', 'Group Policy', 'PowerShell'],
    emoji: '🏢',
    color: 'from-green-500 to-blue-500',
    github: 'https://github.com/ghazalerfani/ad-lab',
    demo: '',
    difficulty: 'Intermediate',
    timeSpent: '6 weeks',
    impact: 'Medium'
  },
  {
    id: 'network-monitoring',
    title: 'Network Monitoring Dashboard',
    category: 'monitoring',
    problem: 'No visibility into network performance and device status',
    solution: 'Deployed PRTG Network Monitor with custom sensors and alerting',
    result: 'Achieved 99.5% network uptime monitoring and proactive issue detection',
    tags: ['PRTG', 'SNMP', 'Network Monitoring', 'Alerting'],
    emoji: '📊',
    color: 'from-orange-500 to-red-500',
    github: 'https://github.com/ghazalerfani/network-monitoring',
    demo: '',
    difficulty: 'Intermediate',
    timeSpent: '4 weeks',
    impact: 'High'
  },
  {
    id: 'powershell-scripts',
    title: 'PowerShell Administration Scripts',
    category: 'automation',
    problem: 'Manual user account management was time-consuming and error-prone',
    solution: 'Created PowerShell scripts for bulk user creation, password resets, and reporting',
    result: 'Reduced user management tasks from hours to minutes',
    tags: ['PowerShell', 'Active Directory', 'User Management', 'Automation'],
    emoji: '📜',
    color: 'from-green-500 to-teal-500',
    github: 'https://github.com/ghazalerfani/powershell-admin',
    demo: '',
    difficulty: 'Beginner',
    timeSpent: '3 weeks',
    impact: 'Medium'
  },
  {
    id: 'backup-solution',
    title: 'Small Business Backup Solution',
    category: 'data-protection',
    problem: 'Small office had no reliable backup strategy for critical data',
    solution: 'Implemented automated backup solution using Windows Backup and cloud storage',
    result: 'Established 3-2-1 backup strategy with 99% reliability',
    tags: ['Windows Backup', 'Cloud Storage', 'Data Protection', 'Scheduling'],
    emoji: '💾',
    color: 'from-purple-500 to-indigo-500',
    github: 'https://github.com/ghazalerfani/backup-solution',
    demo: '',
    difficulty: 'Beginner',
    timeSpent: '2 weeks',
    impact: 'High'
  },
  {
    id: 'inventory-system',
    title: 'IT Asset Inventory System',
    category: 'asset-management',
    problem: 'No centralized tracking of IT equipment and software licenses',
    solution: 'Built Excel-based inventory system with automated reporting',
    result: 'Improved asset tracking accuracy by 90% and ensured license compliance',
    tags: ['Excel', 'Asset Management', 'Reporting', 'Compliance'],
    emoji: '📋',
    color: 'from-blue-500 to-purple-500',
    github: 'https://github.com/ghazalerfani/asset-inventory',
    demo: '',
    difficulty: 'Beginner',
    timeSpent: '3 weeks',
    impact: 'Medium'
  }
];

interface ProjectsGridProps {
  selectedFilter: string;
}

export function ProjectsGrid({ selectedFilter }: ProjectsGridProps) {
  const [expandedProject, setExpandedProject] = useState<string | null>(null);
  const [hoveredProject, setHoveredProject] = useState<string | null>(null);

  const filteredProjects = selectedFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedFilter);

  const handleProjectClick = (projectId: string) => {
    setExpandedProject(expandedProject === projectId ? null : projectId);
  };

  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <AnimatePresence mode="popLayout">
        {filteredProjects.map((project, index) => (
          <ProjectCard
            key={project.id}
            project={project}
            index={index}
            isExpanded={expandedProject === project.id}
            isHovered={hoveredProject === project.id}
            onClick={() => handleProjectClick(project.id)}
            onHover={() => setHoveredProject(project.id)}
            onLeave={() => setHoveredProject(null)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

interface ProjectCardProps {
  project: typeof projects[0];
  index: number;
  isExpanded: boolean;
  isHovered: boolean;
  onClick: () => void;
  onHover: () => void;
  onLeave: () => void;
}

function ProjectCard({ 
  project, 
  index, 
  isExpanded, 
  isHovered, 
  onClick, 
  onHover, 
  onLeave 
}: ProjectCardProps) {
  const [clickCount, setClickCount] = useState(0);
  const [showParticles, setShowParticles] = useState(false);

  const handleCardClick = () => {
    setClickCount(prev => prev + 1);
    onClick();
    
    if (clickCount > 0 && clickCount % 3 === 0) {
      setShowParticles(true);
      setTimeout(() => setShowParticles(false), 2000);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-orange-400';
      case 'Expert': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'Low': return 'text-gray-400';
      case 'Medium': return 'text-blue-400';
      case 'High': return 'text-purple-400';
      case 'Critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -5 }}
      onHoverStart={onHover}
      onHoverEnd={onLeave}
      className="relative"
    >
      <Card 
        className={`
          cursor-pointer transition-all duration-300 overflow-hidden h-full
          bg-gradient-to-br ${project.color}/10 
          border-2 border-transparent
          hover:border-primary/50 hover:shadow-2xl hover:shadow-primary/20
          ${isExpanded ? 'ring-2 ring-primary ring-opacity-75' : ''}
        `}
        onClick={handleCardClick}
      >
        <CardContent className="p-6 relative">
          {/* Floating particles */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {project.tags.slice(0, 3).map((tag, i) => (
              <motion.div
                key={i}
                className="absolute text-xs opacity-10 font-mono"
                style={{
                  left: `${20 + i * 25}%`,
                  top: `${10 + i * 20}%`,
                }}
                animate={{
                  y: [0, -15, 0],
                  opacity: isHovered ? [0.1, 0.3, 0.1] : [0.05, 0.15, 0.05]
                }}
                transition={{
                  duration: 3 + i,
                  repeat: Infinity,
                  delay: i * 0.5
                }}
              >
                {tag}
              </motion.div>
            ))}
          </div>

          {/* Particle explosion effect */}
          <AnimatePresence>
            {showParticles && (
              <div className="absolute inset-0 pointer-events-none">
                {Array.from({ length: 8 }).map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ 
                      scale: 0, 
                      x: 0, 
                      y: 0, 
                      opacity: 1 
                    }}
                    animate={{
                      scale: [0, 1, 0],
                      x: (((i * 123) % 100) - 50) * 2,
                      y: (((i * 456) % 100) - 50) * 2,
                      opacity: [1, 1, 0]
                    }}
                    transition={{ 
                      duration: 1.5,
                      delay: i * 0.1,
                      ease: "easeOut"
                    }}
                    className="absolute top-1/2 left-1/2 text-lg"
                  >
                    {project.emoji}
                  </motion.div>
                ))}
              </div>
            )}
          </AnimatePresence>

          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <motion.div
                className="text-3xl"
                animate={isExpanded ? {
                  scale: [1, 1.2, 1],
                  rotate: [0, 360, 0]
                } : {}}
                transition={{ duration: 0.5 }}
              >
                {project.emoji}
              </motion.div>
              
              <div className="flex space-x-1">
                <div className={`text-xs px-2 py-1 rounded-full bg-black/20 ${getDifficultyColor(project.difficulty)}`}>
                  {project.difficulty}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full bg-black/20 ${getImpactColor(project.impact)}`}>
                  {project.impact}
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors">
              {project.title}
            </h3>

            <div className="space-y-3 text-sm">
              <div>
                <span className="font-semibold text-red-400">🔧 Problem:</span>
                <p className="text-muted-foreground mt-1">{project.problem}</p>
              </div>

              <div>
                <span className="font-semibold text-blue-400">🛠️ Solution:</span>
                <p className="text-muted-foreground mt-1">{project.solution}</p>
              </div>

              <div>
                <span className="font-semibold text-green-400">✅ Result:</span>
                <p className="text-muted-foreground mt-1">{project.result}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mt-4">
              {project.tags.map((tag, i) => (
                <motion.span
                  key={tag}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: i * 0.1 }}
                  whileHover={{ scale: 1.1 }}
                  className="text-xs px-2 py-1 bg-primary/20 text-primary rounded-full"
                >
                  {tag}
                </motion.span>
              ))}
            </div>

            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 pt-4 border-t border-border"
                >
                  <div className="grid grid-cols-2 gap-4 text-xs mb-4">
                    <div>
                      <span className="font-semibold">Time Spent:</span>
                      <p className="text-muted-foreground">{project.timeSpent}</p>
                    </div>
                    <div>
                      <span className="font-semibold">Impact:</span>
                      <p className={getImpactColor(project.impact)}>{project.impact}</p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    {project.github && (
                      <Button size="sm" variant="outline" asChild>
                        <a href={project.github} target="_blank" rel="noopener noreferrer">
                          <Github className="w-4 h-4 mr-2" />
                          Code
                        </a>
                      </Button>
                    )}
                    {project.demo && (
                      <Button size="sm" variant="outline" asChild>
                        <a href={project.demo} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Demo
                        </a>
                      </Button>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="mt-4 text-xs text-muted-foreground/60">
              {clickCount > 0 && `Clicked ${clickCount} times • `}
              {isExpanded ? 'Click to collapse' : 'Click to expand'}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
