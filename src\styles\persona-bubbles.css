/* Enhanced responsive headshot styling */
.headshot-container {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .headshot-container {
    border: 3px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
  }
}

@media (min-width: 1024px) {
  .headshot-container {
    border: 4px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  }
}

.headshot-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  /* Enhanced background removal effect */
  filter: contrast(1.1) saturate(1.2) brightness(1.05);
  background: radial-gradient(circle, transparent 60%, rgba(255, 255, 255, 0.1) 100%);
}

/* Responsive orbital animation keyframes with increased spacing */
@keyframes orbit-clockwise-mobile {
  from {
    transform: rotate(0deg) translateX(180px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(180px) rotate(-360deg);
  }
}

@keyframes orbit-clockwise-tablet {
  from {
    transform: rotate(0deg) translateX(280px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(280px) rotate(-360deg);
  }
}

@keyframes orbit-clockwise-desktop {
  from {
    transform: rotate(0deg) translateX(380px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(380px) rotate(-360deg);
  }
}

@keyframes orbit-counterclockwise-mobile {
  from {
    transform: rotate(0deg) translateX(180px) rotate(0deg);
  }
  to {
    transform: rotate(-360deg) translateX(180px) rotate(360deg);
  }
}

@keyframes orbit-counterclockwise-tablet {
  from {
    transform: rotate(0deg) translateX(280px) rotate(0deg);
  }
  to {
    transform: rotate(-360deg) translateX(280px) rotate(360deg);
  }
}

@keyframes orbit-counterclockwise-desktop {
  from {
    transform: rotate(0deg) translateX(380px) rotate(0deg);
  }
  to {
    transform: rotate(-360deg) translateX(380px) rotate(360deg);
  }
}

/* Connection line animation */
@keyframes connection-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleY(1.1);
  }
}

.connection-line {
  animation: connection-pulse 2s ease-in-out infinite;
}

/* Enhanced responsive persona bubble effects */
.persona-bubble {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.persona-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.persona-bubble:hover::before {
  transform: translateX(100%);
}

.persona-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Responsive scaling for different screen sizes */
@media (max-width: 640px) {
  .persona-bubble:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  }
}

/* Particle animation */
@keyframes particle-float {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-40px) rotate(360deg);
    opacity: 0;
  }
}

.particle {
  animation: particle-float 3s ease-out infinite;
}

/* Glow effect for active persona */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.6);
  }
}

.active-glow {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* Enhanced responsive adjustments with smooth transitions */
@media (max-width: 1024px) {
  .orbit-container {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .orbit-container {
    padding: 0.5rem;
  }

  .persona-bubble {
    backdrop-filter: blur(8px);
  }
}

@media (max-width: 640px) {
  .orbit-container {
    padding: 0.25rem;
  }

  .persona-bubble {
    backdrop-filter: blur(6px);
  }
}

@media (max-width: 480px) {
  .orbit-container {
    padding: 0.125rem;
  }
}

/* Smooth transitions for all elements */
* {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
