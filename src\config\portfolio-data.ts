import type { Persona } from '@/types/portfolio';

export const portfolioData: Record<Persona['id'], Persona> = {
  'engineer': {
    id: 'engineer',
    title: 'IT Operations Specialist',
    emoji: '💻',
    color: 'rgba(99, 102, 241, 1)',
    description: 'Passionate about maintaining robust IT infrastructure and providing exceptional technical support. I focus on system reliability, user satisfaction, and continuous improvement of IT operations.',
    inspirations: [
      {
        name: '<PERSON><PERSON>',
        role: 'Creator of Linux',
        image: '/inspirations/linus-torvalds.jpg',
        lessons: [
          'The importance of open-source collaboration in technology',
          'Building systems that are reliable and scalable',
          'Never stop learning and adapting to new technologies',
          'Quality code and documentation make all the difference'
        ]
      },
      {
        name: '<PERSON>',
        role: 'Computer Programming Pioneer',
        image: '/inspirations/grace-hopper.jpg',
        lessons: [
         'Breaking barriers in technology with determination',
         'The power of clear communication in technical fields',
         'Innovation comes from questioning the status quo',
         'Mentoring others amplifies your impact'
        ]
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        role: 'CEO of Microsoft',
        image: '/inspirations/satya-nadella.jpg',
        lessons: [
          'Empathy and growth mindset drive technological innovation',
          'Cloud computing transforms how we deliver IT services',
          'Inclusive leadership creates stronger technical teams',
          'Customer-first approach in IT operations'
        ]
      },
      {
        name: 'Kelsey Hightower',
        role: 'Cloud Native Advocate',
        image: '/inspirations/kelsey-hightower.jpg',
        lessons: [
          'Automation and orchestration are key to modern IT',
          'Sharing knowledge through teaching and speaking',
          'Simplifying complex systems for better operations',
          'Community involvement accelerates professional growth'
        ]
      }
    ],
    experiences: [
      {
        title: 'IT Operations Foundations',
        description: 'Mastered operational IT skills by conducting technology health checks, configuring and monitoring services, and implementing comprehensive IT solutions. Gained hands-on experience with capacity and performance analysis, determining resource requirements for CPU, memory, and storage optimization.',
        date: '2024',
        tags: ['IT Operations', 'Performance Analysis', 'Service Monitoring', 'Resource Management', 'Health Checks'],
        link: 'https://www.rrc.ca'
      },
      {
        title: 'IT Service Management (COMP-1309)',
        description: 'Equipped with technical, procedural, and interpersonal skills for IT Operations roles. Applied structured troubleshooting techniques including root cause analysis and hypothesis testing. Practiced change management lifecycle including planning, approval, and implementation of change requests (RFCs).',
        date: '2024',
        tags: ['ITIL', 'Change Management', 'Incident Management', 'Root Cause Analysis', 'Service Management'],
        link: ''
      },
      {
        title: 'Security Foundations',
        description: 'Gained foundational understanding of information security by defining security terminology and analyzing security threats. Mastered CIA triad principles (Confidentiality, Integrity, Availability), least privilege, defense-in-depth, and Zero Trust concepts. Applied security principles to secure modern IT environments.',
        date: '2024',
        tags: ['Cybersecurity', 'CIA Triad', 'Zero Trust', 'Security Analysis', 'Risk Assessment'],
        link: ''
      },
      {
        title: 'IT Service Desk Customer Support',
        description: 'Enhanced customer service and technical support skills by troubleshooting issues and managing incidents with service desk methodologies. Developed communication strategies for technical and interpersonal aspects of IT roles, ensuring readiness for workplace scenarios.',
        date: '2024',
        tags: ['Service Desk', 'Customer Support', 'Incident Management', 'Technical Communication', 'Troubleshooting'],
        link: ''
      },
      {
        title: 'Communication for the Workplace',
        description: 'Advanced career-oriented communication skills by evaluating personal strengths and creating actionable career development goals. Developed strategic planning skills essential for IT Operations roles, from job hunting to professional growth.',
        date: '2024',
        tags: ['Professional Communication', 'Career Development', 'Strategic Planning', 'Workplace Skills', 'Self-Assessment'],
        link: ''
      },
      {
        title: 'People, Processes, and Technology Integration',
        description: 'Gained ability to integrate people, processes, and technology to support service delivery by creating deployment plans. Learned to bridge technical solutions with human and operational needs, ensuring smooth IT service delivery and stakeholder satisfaction.',
        date: '2024',
        tags: ['Service Delivery', 'Process Integration', 'Stakeholder Management', 'Deployment Planning', 'Technology Integration'],
        link: ''
      },
      {
        title: 'Python Programming & Web Development',
        description: 'Mastered Python programming fundamentals including variables, functions, data types, control flow, and error handling. Successfully created modules, packages, and command-line scripts. Also gained proficiency in HTML and CSS for web development projects.',
        date: '2024',
        tags: ['Python', 'HTML', 'CSS', 'Programming', 'Web Development'],
        link: ''
      }
    ]
  },
  'educator': {
    id: 'educator',
    title: 'Knowledge Facilitator',
    emoji: '🤝',
    color: 'rgba(139, 92, 246, 1)',
    description: 'Dedicated to sharing knowledge and helping others grow in their technical journey. I believe in making complex IT concepts accessible and fostering collaborative learning environments.',
    inspirations: [
      {
        name: 'Sal Khan',
        role: 'Founder of Khan Academy',
        image: '/inspirations/sal-khan.jpg',
        lessons: [
          'Free, accessible education can transform lives',
          'Breaking down complex topics into digestible parts',
          'Technology can scale personalized learning',
          'Patience and persistence in teaching are essential'
        ]
      },
      {
        name: 'Reshma Saujani',
        role: 'Founder of Girls Who Code',
        image: '/inspirations/reshma-saujani.jpg',
        lessons: [
          'Diversity in tech starts with education and mentorship',
          'Creating safe spaces for learning and growth',
          'Advocacy and education go hand in hand',
          'Empowering others multiplies your impact'
        ]
      },
      {
        name: 'Quincy Larson',
        role: 'Founder of freeCodeCamp',
        image: '/inspirations/quincy-larson.jpg',
        lessons: [
              'Open-source education democratizes learning',
              'Community-driven learning is incredibly powerful',
              'Practical, project-based learning works best',
              'Building while learning accelerates skill development'
        ]
      }
    ],
    experiences: [
      {
        title: 'Business Language Certificate (CLB 8)',
        description: 'Completed advanced business communication training at Red River College, focusing on business relationships and communication techniques including conflict resolution. Developed skills essential for professional IT environments and cross-cultural communication.',
        date: '2024',
        tags: ['Business Communication', 'Conflict Resolution', 'Professional Development', 'Cross-Cultural Communication'],
        link: ''
      },
      {
        title: 'Communication Strategies (COMM-1173)',
        description: 'Gained foundational and advanced strategies to excel in both technical and interpersonal aspects of IT roles. Developed communication tools, self-awareness, and strategic planning skills essential for IT Operations positions and team collaboration.',
        date: '2024',
        tags: ['Communication Strategies', 'Technical Communication', 'Interpersonal Skills', 'Strategic Planning'],
        link: ''
      },
      {
        title: 'Digital Literacy Certification (ICDL)',
        description: 'Demonstrated proficiency in a wide range of digital skills, including advanced use of Microsoft Office Suite. Achieved international certification validating comprehensive digital competencies essential for modern workplace environments.',
        date: '2024',
        tags: ['Digital Literacy', 'Microsoft Office', 'International Certification', 'Workplace Skills'],
        link: ''
      },
      {
        title: 'AI and Technology Training',
        description: 'Completed multiple courses in emerging technologies including Introduction to Conversational AI, AI Productivity Hacks, and AI Trends. Learned to leverage generative AI tools to automate routine tasks, enhance collaboration, and improve decision-making processes.',
        date: '2024',
        tags: ['Artificial Intelligence', 'Conversational AI', 'Productivity Tools', 'Emerging Technologies'],
        link: ''
      },
      {
        title: 'Multilingual Communication Skills',
        description: 'Developed essential French language skills for workplace communication. Combined with existing multilingual abilities, this enhances capacity to support diverse user communities and collaborate in multicultural IT environments.',
        date: '2024',
        tags: ['French Language', 'Multilingual Support', 'Cultural Competency', 'Workplace Communication'],
        link: ''
      }
    ]
  },
  'movement-builder': {
    id: 'movement-builder',
    title: 'Community Connector',
    emoji: '🎨',
    color: 'rgba(236, 72, 153, 1)',
    description: 'Committed to building inclusive tech communities and bridging the gap between technical expertise and meaningful social impact. I believe technology should serve everyone.',
    inspirations: [
      {
        name: 'Kimberly Bryant',
        role: 'Founder of Black Girls CODE',
        image: '/inspirations/kimberly-bryant.jpg',
        lessons: [
          'Representation matters in technology fields',
          'Early exposure to tech can change life trajectories',
          'Community programs create lasting impact',
          'Persistence in advocacy drives real change'
        ]
      },
      {
        name: 'Tarana Burke',
        role: 'Civil Rights Activist',
        image: '/inspirations/tarana-burke.jpg',
        lessons: [
            'Grassroots movements can create global change',
            'Technology amplifies voices and movements',
            'Healing and empowerment go hand in hand',
            'Community support is essential for growth'
        ]
      },
      {
        name: 'Leah Culver',
        role: 'Tech Entrepreneur & Advocate',
        image: '/inspirations/leah-culver.jpg',
        lessons: [
            'Building inclusive tech products from the start',
            'Mentorship and sponsorship create opportunities',
            'Diverse teams build better solutions',
            'Using privilege to lift others up'
        ]
      },
      {
        name: 'Ayana Elizabeth Johnson',
        role: 'Climate Policy Expert',
        image: '/inspirations/ayana-johnson.jpg',
        lessons: [
            'Technology can address global challenges',
            'Interdisciplinary collaboration drives innovation',
            'Communication makes complex issues accessible',
            'Optimism and action can coexist with realism'
        ]
      }
    ],
    experiences: [
      {
        title: 'Store Associate - Marshalls/Home Sense, St. Vital Mall',
        description: 'Developed troubleshooting mindset through company training on resolving customer and operational issues, building analytical thinking essential for technical problem-solving. Gained experience in risk assessment, compliance understanding, and safety protocols directly applicable to IT security practices.',
        date: '2023-2024',
        tags: ['Customer Service', 'Problem Solving', 'Risk Assessment', 'Compliance', 'Team Collaboration'],
        link: ''
      },
      {
        title: 'Health & Safety Committee Member',
        description: 'Participated in workplace health and safety initiatives, developing risk assessment skills and compliance understanding. These experiences directly translate to IT security practices, risk management, and maintaining safe technology environments.',
        date: '2023-2024',
        tags: ['Health & Safety', 'Risk Management', 'Compliance', 'Committee Work', 'Safety Protocols'],
        link: ''
      },
      {
        title: 'Joy Project Event Coordinator',
        description: 'Demonstrated project management, team collaboration, and stakeholder engagement skills through event planning and execution. Combined creative vision with strategic planning to connect community members with valuable opportunities and resources.',
        date: '2023-2024',
        tags: ['Project Management', 'Event Planning', 'Team Collaboration', 'Stakeholder Engagement', 'Community Building'],
        link: ''
      },
      {
        title: 'Creative Design & Digital Art',
        description: 'Applied henna art designs and created intricate patterns for community events, receiving positive feedback and volunteer invitations. Developed digital artwork using design tools like MediBang Paint, Photoshop, and Canva, successfully completing design projects.',
        date: '2023-2024',
        tags: ['Creative Design', 'Digital Art', 'Community Events', 'Design Tools', 'Artistic Skills'],
        link: ''
      },
      {
        title: 'UX/UI Design Development',
        description: 'Gained practical UX design skills through Adobe XD training, learning to create and share prototypes, build layouts, use components, and add interactions. Developed understanding of user experience principles and design thinking methodologies.',
        date: '2024',
        tags: ['UX Design', 'UI Design', 'Adobe XD', 'Prototyping', 'Design Thinking'],
        link: ''
      },
      {
        title: 'Cross-Cultural Communication & Marketing',
        description: 'Combined creative vision with strategic marketing to connect students and community members with valuable opportunities. Demonstrated advanced problem-solving skills and digital marketing experience through cross-channel campaign management.',
        date: '2023-2024',
        tags: ['Marketing', 'Cross-Cultural Communication', 'Strategic Planning', 'Campaign Management', 'Community Engagement'],
        link: ''
      }
    ]
  }
}; 
