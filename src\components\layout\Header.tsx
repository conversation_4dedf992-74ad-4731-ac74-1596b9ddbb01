import { cn } from '@/lib/utils';
import { Navigation } from '@/components/Navigation';
import { MobileNav } from '@/components/MobileNav';
import { ThemeToggle } from '@/components/ThemeToggle';
import Link from 'next/link';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  return (
    <header className={cn(
      'fixed top-0 left-0 right-0 z-50 py-4 px-6',
      'bg-gradient-to-b from-background/90 to-background/0 backdrop-blur-sm border-b border-border/50',
      className
    )}>
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <Link href="/" className="group">
          <h1 className="text-2xl font-bold tracking-tight relative">
            <span className="animate-shimmer bg-[linear-gradient(110deg,#fff,15%,#6366F1,35%,#8B5CF6,50%,#EC4899,65%,#fff,85%,#fff)] bg-[length:200%_100%] [-webkit-background-clip:text] [-webkit-text-fill-color:transparent] group-hover:animate-pulse">
              <PERSON><PERSON><PERSON>
            </span>
          </h1>
          <p className="text-sm text-muted-foreground group-hover:text-primary transition-colors">
            IT Support • Automation • Creative Tech
          </p>
        </Link>

        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <div className="md:hidden">
              <MobileNav />
            </div>
          </div>
          <Navigation className="ml-auto" />
        </div>
      </div>
    </header>
  );
}