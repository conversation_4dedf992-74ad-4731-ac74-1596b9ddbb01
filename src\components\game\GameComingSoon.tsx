'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const pixelCharacters = ['🧑‍💻', '👾', '🍄', '⭐', '💎', '🔥', '⚡', '🌟'];
const gameFeatures = [
  { icon: '🏃‍♀️', title: 'Side-scrolling Adventure', description: 'Navigate through tech-themed levels' },
  { icon: '🎨', title: 'Pixel Art Graphics', description: 'Hand-crafted retro-style visuals' },
  { icon: '🎵', title: 'Chiptune Soundtrack', description: 'Nostalgic 8-bit music' },
  { icon: '🏆', title: 'Achievement System', description: 'Unlock coding challenges' },
  { icon: '🌍', title: 'Multiple Worlds', description: 'Frontend, Backend, DevOps realms' },
  { icon: '👥', title: 'Multiplayer Mode', description: 'Code together, debug together' }
];

export function GameComingSoon() {
  const [progress, setProgress] = useState(67);
  const [selected<PERSON>haracter, setSelected<PERSON>haracter] = useState<string | null>(null);
  const [showMiniGame, setShowMiniGame] = useState(false);
  const [score, setScore] = useState(0);
  const [gameStarted, setGameStarted] = useState(false);
  const [fallingObjects, setFallingObjects] = useState<Array<{id: number, x: number, y: number, emoji: string}>>([]);

  // Simulate development progress
  useEffect(() => {
    let counter = 0;
    const interval = setInterval(() => {
      setProgress(prev => {
        counter++;
        const increment = 0.1 + (counter % 5) * 0.1; // Deterministic increment
        const newProgress = prev + increment;
        return newProgress > 100 ? 67 : newProgress;
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Mini-game logic
  useEffect(() => {
    if (!gameStarted) return;

    let gameCounter = 0;
    const gameInterval = setInterval(() => {
      setFallingObjects(prev => {
        // Add new falling object
        const newObjects = [...prev];
        gameCounter++;
        if (gameCounter % 3 === 0) { // Deterministic spawn rate
          newObjects.push({
            id: Date.now(),
            x: (gameCounter * 37) % 90, // Deterministic position
            y: -5,
            emoji: pixelCharacters[gameCounter % pixelCharacters.length]
          });
        }

        // Move objects down and remove off-screen ones
        return newObjects
          .map(obj => ({ ...obj, y: obj.y + 2 }))
          .filter(obj => obj.y < 100);
      });
    }, 100);

    return () => clearInterval(gameInterval);
  }, [gameStarted]);

  const handleCharacterClick = (character: string) => {
    setSelectedCharacter(character);
    setScore(prev => prev + 10);
  };

  const handleObjectClick = (objectId: number) => {
    setFallingObjects(prev => prev.filter(obj => obj.id !== objectId));
    setScore(prev => prev + 50);
  };

  const startMiniGame = () => {
    setShowMiniGame(true);
    setGameStarted(true);
    setScore(0);
    setFallingObjects([]);
  };

  const stopMiniGame = () => {
    setShowMiniGame(false);
    setGameStarted(false);
    setFallingObjects([]);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Main Coming Soon Card */}
      <Card className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 border-purple-500/30 overflow-hidden">
        <CardContent className="p-12 text-center relative">
          {/* Animated background elements */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute text-2xl opacity-10"
                style={{
                  left: `${(i * 123) % 100}%`,
                  top: `${(i * 456) % 100}%`,
                }}
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 360],
                  opacity: [0.1, 0.3, 0.1]
                }}
                transition={{
                  duration: 5 + (i * 789) % 5,
                  repeat: Infinity,
                  delay: (i * 111) % 2
                }}
              >
                {pixelCharacters[i % pixelCharacters.length]}
              </motion.div>
            ))}
          </div>

          <div className="relative z-10">
            <motion.div
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ duration: 3, repeat: Infinity }}
              className="text-8xl mb-6"
            >
              🎮
            </motion.div>

            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Game in Development
            </h1>

            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              I'm working on a side-scrolling game with a mini-me avatar. 
              Stay tuned for something fun that combines coding challenges with retro gaming!
            </p>

            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex justify-between text-sm mb-2">
                <span>Development Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-4 overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
              <motion.div
                className="text-xs text-muted-foreground mt-2"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {progress < 70 ? 'Building core mechanics...' : 
                 progress < 85 ? 'Adding pixel art assets...' : 
                 'Polishing and testing...'}
              </motion.div>
            </div>

            {/* Character Selection Preview */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">Choose Your Avatar</h3>
              <div className="flex justify-center space-x-4">
                {pixelCharacters.slice(0, 4).map((character, index) => (
                  <motion.div
                    key={character}
                    className={`
                      text-4xl cursor-pointer p-3 rounded-lg transition-all duration-300
                      ${selectedCharacter === character 
                        ? 'bg-primary/20 ring-2 ring-primary ring-opacity-75' 
                        : 'hover:bg-muted/50'
                      }
                    `}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => handleCharacterClick(character)}
                    animate={selectedCharacter === character ? {
                      y: [0, -10, 0],
                      rotate: [0, 360, 0]
                    } : {}}
                    transition={{ duration: 0.5 }}
                  >
                    {character}
                  </motion.div>
                ))}
              </div>
              {selectedCharacter && (
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-primary mt-2"
                >
                  Great choice! {selectedCharacter} will be your coding companion!
                </motion.p>
              )}
            </div>

            {/* Mini Game Button */}
            <Button
              onClick={showMiniGame ? stopMiniGame : startMiniGame}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-8 rounded-full text-lg shadow-lg"
            >
              {showMiniGame ? '🛑 Stop Mini-Game' : '🎯 Try Mini-Game Preview'}
            </Button>

            {gameStarted && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 text-lg font-bold text-primary"
              >
                Score: {score}
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Mini Game */}
      <AnimatePresence>
        {showMiniGame && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="relative"
          >
            <Card className="bg-black/90 border-green-500/50 overflow-hidden">
              <CardContent className="p-0">
                <div className="relative w-full h-64 bg-gradient-to-b from-blue-900/50 to-green-900/50 overflow-hidden">
                  {/* Game area */}
                  <div className="absolute inset-0">
                    {/* Falling objects */}
                    {fallingObjects.map((obj) => (
                      <motion.div
                        key={obj.id}
                        className="absolute text-2xl cursor-pointer hover:scale-125 transition-transform"
                        style={{
                          left: `${obj.x}%`,
                          top: `${obj.y}%`,
                        }}
                        onClick={() => handleObjectClick(obj.id)}
                        whileHover={{ scale: 1.3 }}
                        whileTap={{ scale: 0.8 }}
                      >
                        {obj.emoji}
                      </motion.div>
                    ))}

                    {/* Player character */}
                    <motion.div
                      className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-4xl"
                      animate={{
                        y: [0, -5, 0],
                      }}
                      transition={{ duration: 1, repeat: Infinity }}
                    >
                      {selectedCharacter || '🧑‍💻'}
                    </motion.div>

                    {/* Instructions */}
                    <div className="absolute top-4 left-4 text-green-400 font-mono text-sm">
                      <div>Click falling objects to catch them!</div>
                      <div>Score: {score}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Game Features */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {gameFeatures.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.05 }}
          >
            <Card className="bg-gradient-to-br from-muted/50 to-accent/50 border-muted/20 h-full">
              <CardContent className="p-6 text-center">
                <motion.div
                  className="text-4xl mb-3"
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, delay: index * 0.2 }}
                >
                  {feature.icon}
                </motion.div>
                <h3 className="font-bold mb-2">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Development Updates */}
      <Card className="bg-gradient-to-br from-green-500/10 to-blue-500/10 border-green-500/20">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <motion.span
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mr-2"
            >
              ⚙️
            </motion.span>
            Development Updates
          </h3>
          
          <div className="space-y-3 text-sm">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-3"
            >
              <span className="text-green-400">✅</span>
              <span>Core game engine implemented</span>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="flex items-center space-x-3"
            >
              <span className="text-green-400">✅</span>
              <span>Character animations and sprites created</span>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="flex items-center space-x-3"
            >
              <span className="text-yellow-400">🔄</span>
              <span>Level design and obstacles (in progress)</span>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="flex items-center space-x-3"
            >
              <span className="text-gray-400">⏳</span>
              <span>Sound effects and music (planned)</span>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="flex items-center space-x-3"
            >
              <span className="text-gray-400">⏳</span>
              <span>Multiplayer functionality (future update)</span>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* Newsletter Signup */}
      <Card className="bg-gradient-to-r from-purple-600/10 to-pink-600/10 border-purple-500/20">
        <CardContent className="p-6 text-center">
          <h3 className="text-xl font-bold mb-4">Get Notified When It's Ready!</h3>
          <p className="text-muted-foreground mb-4">
            Be the first to play when the game launches. I'll send you a personal invite!
          </p>
          <div className="flex max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-2 rounded-l-lg border border-r-0 border-muted bg-background"
            />
            <Button className="rounded-l-none bg-gradient-to-r from-purple-600 to-pink-600">
              Notify Me! 🚀
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
