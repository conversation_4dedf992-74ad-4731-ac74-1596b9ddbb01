'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Github, Linkedin, Mail, Download, ExternalLink } from 'lucide-react';

const socialLinks = [
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: Linkedin,
    url: 'https://linkedin.com/in/ghazal-erfani',
    color: 'from-blue-600 to-blue-700',
    description: 'Professional network & career updates',
    hoverEffect: 'bounce'
  },
  {
    id: 'github',
    name: 'GitHub',
    icon: Github,
    url: 'https://github.com/ghazal-erfani',
    color: 'from-gray-700 to-gray-900',
    description: 'Code repositories & open source projects',
    hoverEffect: 'rotate'
  },
  {
    id: 'email',
    name: 'Email',
    icon: Mail,
    url: 'mailto:<EMAIL>',
    color: 'from-red-500 to-red-600',
    description: 'Direct communication for opportunities',
    hoverEffect: 'pulse'
  }
];

const quickFacts = [
  { emoji: '⚡', text: 'Usually responds within 24 hours' },
  { emoji: '🌍', text: 'Available for remote opportunities' },
  { emoji: '🤝', text: 'Open to collaborations & mentoring' },
  { emoji: '☕', text: 'Always up for a coffee chat' }
];

export function ContactInfo() {
  const [hoveredLink, setHoveredLink] = useState<string | null>(null);
  const [clickedLink, setClickedLink] = useState<string | null>(null);
  const [showResume, setShowResume] = useState(false);

  const handleLinkClick = (linkId: string, url: string) => {
    setClickedLink(linkId);
    setTimeout(() => setClickedLink(null), 500);
    window.open(url, '_blank');
  };

  const handleResumeDownload = () => {
    setShowResume(true);
    // Simulate download
    setTimeout(() => setShowResume(false), 2000);
  };

  return (
    <div className="space-y-6">
      {/* Social Links */}
      <Card className="bg-gradient-to-br from-secondary/50 to-accent/50 border-secondary/20 overflow-hidden">
        <CardContent className="p-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-6"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="text-3xl mb-3"
            >
              🚀
            </motion.div>
            <h3 className="text-xl font-bold mb-2">Let's Connect!</h3>
            <p className="text-sm text-muted-foreground">
              Find me on these platforms
            </p>
          </motion.div>

          <div className="space-y-4">
            {socialLinks.map((link, index) => (
              <motion.div
                key={link.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onHoverStart={() => setHoveredLink(link.id)}
                onHoverEnd={() => setHoveredLink(null)}
              >
                <Button
                  variant="outline"
                  className={`
                    w-full p-4 h-auto justify-start transition-all duration-300
                    ${hoveredLink === link.id 
                      ? `bg-gradient-to-r ${link.color} text-white border-transparent shadow-lg` 
                      : 'hover:border-primary/50'
                    }
                    ${clickedLink === link.id ? 'animate-pulse' : ''}
                  `}
                  onClick={() => handleLinkClick(link.id, link.url)}
                >
                  <motion.div
                    className="flex items-center space-x-3 w-full"
                    animate={hoveredLink === link.id ? {
                      x: link.hoverEffect === 'bounce' ? [0, 5, 0] : 0,
                      rotate: link.hoverEffect === 'rotate' ? [0, 360] : 0,
                      scale: link.hoverEffect === 'pulse' ? [1, 1.1, 1] : 1
                    } : {}}
                    transition={{ duration: 0.5 }}
                  >
                    <link.icon className="w-5 h-5" />
                    <div className="flex-1 text-left">
                      <div className="font-semibold">{link.name}</div>
                      <div className="text-xs opacity-80">{link.description}</div>
                    </div>
                    <ExternalLink className="w-4 h-4 opacity-60" />
                  </motion.div>
                </Button>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Resume Download */}
      <Card className="bg-gradient-to-br from-primary/10 to-purple-600/10 border-primary/20">
        <CardContent className="p-6 text-center">
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-3xl mb-3"
          >
            📄
          </motion.div>
          <h3 className="text-lg font-bold mb-2">Download My Resume</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Get the full details of my experience and skills
          </p>
          
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={handleResumeDownload}
              className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white font-semibold shadow-lg"
            >
              <AnimatePresence mode="wait">
                {showResume ? (
                  <motion.div
                    key="downloading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex items-center space-x-2"
                  >
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      ⚡
                    </motion.div>
                    <span>Downloading...</span>
                  </motion.div>
                ) : (
                  <motion.div
                    key="download"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download Resume</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </CardContent>
      </Card>

      {/* Quick Facts */}
      <Card className="bg-gradient-to-br from-green-500/10 to-blue-500/10 border-green-500/20">
        <CardContent className="p-6">
          <h3 className="text-lg font-bold mb-4 flex items-center">
            <motion.span
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              className="mr-2"
            >
              💡
            </motion.span>
            Quick Facts
          </h3>
          
          <div className="space-y-3">
            {quickFacts.map((fact, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ x: 5, scale: 1.02 }}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-all duration-300 cursor-pointer"
              >
                <motion.span
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                  className="text-lg"
                >
                  {fact.emoji}
                </motion.span>
                <span className="text-sm">{fact.text}</span>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Availability Status */}
      <Card className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border-orange-500/20">
        <CardContent className="p-6 text-center">
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              opacity: [0.8, 1, 0.8]
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className="inline-flex items-center space-x-2 mb-3"
          >
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm font-semibold text-green-400">Available for Opportunities</span>
          </motion.div>
          
          <p className="text-xs text-muted-foreground mb-4">
            Currently seeking full-time positions in IT Operations, 
            System Administration, or DevOps roles
          </p>
          
          <div className="flex justify-center space-x-2 text-xs">
            <span className="px-2 py-1 bg-primary/20 text-primary rounded-full">Full-time</span>
            <span className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded-full">Remote</span>
            <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded-full">Contract</span>
          </div>
        </CardContent>
      </Card>

      {/* Fun Interactive Element */}
      <Card className="bg-gradient-to-br from-pink-500/10 to-purple-500/10 border-pink-500/20">
        <CardContent className="p-6 text-center">
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="cursor-pointer"
            onClick={() => {
              // Easter egg: show a fun message
              alert("🎉 You found the secret button! Thanks for exploring my portfolio so thoroughly!");
            }}
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-4xl mb-2"
            >
              🎯
            </motion.div>
            <p className="text-sm font-medium">
              Click me for a surprise!
            </p>
          </motion.div>
        </CardContent>
      </Card>
    </div>
  );
}
