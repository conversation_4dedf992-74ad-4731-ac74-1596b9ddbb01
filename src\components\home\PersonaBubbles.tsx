'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import '../../styles/persona-bubbles.css';

interface PersonaBubblesProps {
  gameMode: boolean;
  onDiscoverElement: (elementId: string) => void;
  discoveredElements: string[];
}

const personas = [
  {
    id: 'it-support',
    icon: '💻',
    title: 'IT Support Specialist',
    description: 'Network troubleshooting & system optimization',
    shortDesc: 'IT Support',
    href: '/projects',
    color: 'from-blue-500 to-cyan-500',
    angle: -30, // Top-right position (-30 degrees for better spacing)
    particles: ['🔧', '🖥️', '🔌', '📡']
  },
  {
    id: 'automation',
    icon: '🤖',
    title: 'Automation Engineer',
    description: 'Scripts & workflows that streamline operations',
    shortDesc: 'Automation',
    href: '/about',
    color: 'from-purple-500 to-pink-500',
    angle: 110, // Right-bottom position (110 degrees)
    particles: ['⚡', '🔄', '📊', '🎯']
  },
  {
    id: 'creative',
    icon: '🎨',
    title: 'Creative Technologist',
    description: 'Art meets technology for innovative solutions',
    shortDesc: 'Creative Tech',
    href: '/about',
    color: 'from-orange-500 to-red-500',
    angle: 250, // Left-bottom position (250 degrees)
    particles: ['🎨', '✨', '🌈', '💫']
  }
];

export function PersonaBubbles({ gameMode, onDiscoverElement, discoveredElements }: PersonaBubblesProps) {
  const [hoveredPersona, setHoveredPersona] = useState<string | null>(null);
  const [activePersona, setActivePersona] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [particles, setParticles] = useState<Array<{ id: string; x: number; y: number; emoji: string }>>([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Update dimensions on resize for responsive design
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setDimensions({ width: rect.width, height: rect.height });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Calculate responsive orbital radius with increased spacing
  const getOrbitalRadius = () => {
    const minRadius = 180; // Increased minimum radius for mobile
    const maxRadius = 380; // Increased maximum radius for desktop
    const screenWidth = dimensions.width || (typeof window !== 'undefined' ? window.innerWidth : 1024);

    if (screenWidth < 640) return minRadius; // Mobile
    if (screenWidth < 1024) return minRadius + (screenWidth - 640) * 0.3; // Tablet with more spacing
    return maxRadius; // Desktop
  };

  // Auto-cycle through personas every 15 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!hoveredPersona) { // Only auto-cycle if not hovering
        setIsAnimating(true);
        setTimeout(() => {
          setActivePersona(prev => (prev + 1) % personas.length);
          setIsAnimating(false);
        }, 1000);
      }
    }, 15000);

    return () => clearInterval(interval);
  }, [hoveredPersona]);

  // Generate particles for hover effects
  useEffect(() => {
    if (hoveredPersona) {
      const persona = personas.find(p => p.id === hoveredPersona);
      if (persona) {
        const newParticles = Array.from({ length: 8 }, (_, i) => ({
          id: `${hoveredPersona}-${i}`,
          x: (Math.random() - 0.5) * 200,
          y: (Math.random() - 0.5) * 200,
          emoji: persona.particles[Math.floor(Math.random() * persona.particles.length)]
        }));
        setParticles(newParticles);
      }
    } else {
      setParticles([]);
    }
  }, [hoveredPersona]);

  const handlePersonaClick = (persona: typeof personas[0]) => {
    if (gameMode) {
      onDiscoverElement(`persona-click-${persona.id}`);
    }
    window.location.href = persona.href;
  };

  return (
    <div
      ref={containerRef}
      className="relative w-full h-screen flex items-center justify-center overflow-hidden px-6 sm:px-12 md:px-16 lg:px-20 py-12 sm:py-16"
    >
      {/* Responsive container that adapts to screen size */}
      <div className="relative w-full h-full max-w-7xl flex items-center justify-center">

        {/* Central headshot with responsive sizing */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1 }}
            className="relative"
          >
            {/* Responsive headshot container */}
            <div className="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 rounded-full overflow-hidden border-2 sm:border-4 border-white/80 shadow-2xl">
              <Image
                src="/ghazal-headshot.png"
                alt="Ghazal Erfani"
                width={192}
                height={192}
                className="w-full h-full object-cover object-center filter contrast-110 saturate-110 brightness-105"
              />
            </div>

            {/* Orbital ring indicator with increased spacing */}
            <motion.div
              className="absolute inset-0 rounded-full border border-dashed border-white/15"
              style={{
                width: `${getOrbitalRadius() * 2 + 60}px`,
                height: `${getOrbitalRadius() * 2 + 60}px`,
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)'
              }}
              animate={{ rotate: 360 }}
              transition={{
                duration: 80,
                repeat: Infinity,
                ease: "linear"
              }}
            />

            {/* Connection line to active persona */}
            <motion.div
              className="absolute top-1/2 left-1/2 w-0.5 bg-gradient-to-r from-transparent via-white/30 to-transparent pointer-events-none"
              style={{
                height: `${getOrbitalRadius()}px`,
                transformOrigin: 'top center',
              }}
              animate={{
                rotate: personas[activePersona].angle - 90
              }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />
          </motion.div>
        </div>

        {/* Orbiting personas with continuous animation */}
        {personas.map((persona, index) => {
          const radius = getOrbitalRadius();
          const isActive = index === activePersona;
          const isHovered = hoveredPersona === persona.id;

          // Calculate orbital position
          const baseAngle = persona.angle;
          const x = Math.cos((baseAngle * Math.PI) / 180) * radius;
          const y = Math.sin((baseAngle * Math.PI) / 180) * radius;

          // Responsive sizing
          const bubbleSize = isActive || isHovered
            ? 'w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56'
            : 'w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20';

          return (
            <motion.div
              key={persona.id}
              className="absolute cursor-pointer z-10"
              style={{
                left: '50%',
                top: '50%',
              }}
              initial={{
                x: x - (isActive || isHovered ? 128 : 40),
                y: y - (isActive || isHovered ? 128 : 40),
                scale: 0.8,
                opacity: 0
              }}
              animate={{
                x: isActive || isHovered
                  ? (dimensions.width > 1024 ? 450 : dimensions.width > 640 ? 320 : 220) - (isActive || isHovered ? 128 : 40)
                  : x - (isActive || isHovered ? 128 : 40),
                y: isActive || isHovered
                  ? -(isActive || isHovered ? 128 : 40)
                  : y - (isActive || isHovered ? 128 : 40),
                scale: 1,
                opacity: 1,
                rotate: isAnimating && index === activePersona ? [0, 360] : 0
              }}
              transition={{
                duration: isActive || isHovered ? 1.2 : 0.8,
                ease: "easeInOut",
                type: "spring",
                stiffness: 60,
                damping: 15
              }}
              onHoverStart={() => setHoveredPersona(persona.id)}
              onHoverEnd={() => setHoveredPersona(null)}
              onClick={() => handlePersonaClick(persona)}
            >
              {/* Responsive persona bubble */}
              <div className={`
                ${bubbleSize}
                rounded-full bg-gradient-to-br ${persona.color}
                flex flex-col items-center justify-center text-white
                shadow-lg hover:shadow-2xl transition-all duration-500
                border-2 sm:border-3 border-white/30 backdrop-blur-sm
                ${gameMode ? 'ring-2 ring-yellow-400/50' : ''}
                ${isActive ? 'shadow-2xl shadow-white/20' : ''}
                relative overflow-hidden
              `}>
                {/* Icon with responsive sizing */}
                <motion.div
                  className={`${
                    isActive || isHovered
                      ? 'text-3xl sm:text-4xl md:text-5xl mb-2 sm:mb-3'
                      : 'text-sm sm:text-base md:text-lg'
                  } transition-all duration-500`}
                  animate={{
                    rotate: isHovered ? [0, 360] : 0,
                    scale: isHovered ? [1, 1.1, 1] : 1
                  }}
                  transition={{ duration: 0.8 }}
                >
                  {persona.icon}
                </motion.div>

                {/* Expanded content for active/hovered state */}
                <AnimatePresence>
                  {(isActive || isHovered) && (
                    <motion.div
                      initial={{ opacity: 0, y: 20, scale: 0.8 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 20, scale: 0.8 }}
                      className="text-center px-2 sm:px-4"
                    >
                      <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-bold mb-1 sm:mb-2">
                        {persona.title}
                      </h3>
                      <p className="text-xs sm:text-sm opacity-90 leading-relaxed hidden sm:block">
                        {persona.description}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Compact label for small state */}
                <AnimatePresence>
                  {!(isActive || isHovered) && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-xs text-center mt-1 opacity-80 hidden sm:block"
                    >
                      {persona.shortDesc}
                    </motion.p>
                  )}
                </AnimatePresence>

                {/* Pulsing ring for active state */}
                {isActive && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-white/40"
                    animate={{
                      scale: [1, 1.1, 1],
                      opacity: [0.4, 0.8, 0.4]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
              </div>

              {/* Enhanced particle effects for hover */}
              <AnimatePresence>
                {isHovered && (
                  <div className="absolute inset-0 pointer-events-none overflow-visible">
                    {particles.map((particle, i) => (
                      <motion.div
                        key={particle.id}
                        initial={{
                          scale: 0,
                          x: 0,
                          y: 0,
                          opacity: 1,
                          rotate: 0
                        }}
                        animate={{
                          scale: [0, 1.2, 0.8, 0],
                          x: particle.x,
                          y: particle.y,
                          opacity: [1, 1, 0.8, 0],
                          rotate: [0, 180, 360]
                        }}
                        transition={{
                          duration: 2.5,
                          delay: i * 0.15,
                          ease: "easeOut"
                        }}
                        className="absolute top-1/2 left-1/2 text-lg sm:text-xl md:text-2xl"
                      >
                        {particle.emoji}
                      </motion.div>
                    ))}
                  </div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}

        {/* Responsive navigation dots for inactive personas with increased spacing */}
        <div className="absolute left-4 sm:left-8 md:left-12 lg:left-16 top-1/2 transform -translate-y-1/2 space-y-4 sm:space-y-6 md:space-y-8 lg:space-y-10 z-30">
          {personas.map((persona, index) => {
            if (index === activePersona) return null;

            return (
              <motion.div
                key={`nav-${persona.id}`}
                className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 rounded-full bg-gradient-to-br from-gray-600/80 to-gray-800/80 backdrop-blur-sm flex items-center justify-center text-white shadow-lg cursor-pointer hover:shadow-xl border border-white/20"
                whileHover={{
                  scale: 1.1,
                  opacity: 1,
                  borderColor: 'rgba(255, 255, 255, 0.4)'
                }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setActivePersona(index)}
                initial={{ x: -60, opacity: 0 }}
                animate={{ x: 0, opacity: 0.7 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
              >
                <span className="text-sm sm:text-base md:text-lg lg:text-xl">{persona.icon}</span>
              </motion.div>
            );
          })}
        </div>

        {/* Continuous orbital animation indicator with increased spacing */}
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
          style={{
            width: `${getOrbitalRadius() * 2 + 80}px`,
            height: `${getOrbitalRadius() * 2 + 80}px`,
          }}
          animate={{ rotate: 360 }}
          transition={{
            duration: 150,
            repeat: Infinity,
            ease: "linear"
          }}
        >
          <div className="w-full h-full rounded-full border border-dotted border-white/8" />
        </motion.div>

        {/* Game mode indicator with responsive positioning */}
        {gameMode && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute top-4 sm:top-6 md:top-8 right-4 sm:right-6 md:right-8 bg-yellow-400/20 backdrop-blur-sm rounded-full px-3 py-2 sm:px-4 sm:py-2 text-yellow-400 font-mono text-xs sm:text-sm border border-yellow-400/30"
          >
            🎮 Game Mode Active
          </motion.div>
        )}

        {/* Responsive instruction hint */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2 }}
          className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 text-center text-white/60 text-xs sm:text-sm"
        >
          <p className="hidden sm:block">Click the bubbles to explore • Hover for details</p>
          <p className="sm:hidden">Tap to explore</p>
        </motion.div>
      </div>
    </div>
  );
}

export default PersonaBubbles;
