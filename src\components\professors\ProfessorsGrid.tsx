'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';

const professors = [
  {
    id: 'dr-smith',
    name: 'Dr. <PERSON>',
    subject: 'IT Architecture & Systems Design',
    avatar: '👨‍🏫',
    learned: 'Taught me that good architecture is like a well-planned city - everything has its place and purpose.',
    quote: '"The best systems are invisible to their users but indispensable to their success."',
    impact: 'High',
    course: 'Advanced Systems Architecture',
    semester: 'Fall 2023',
    grade: 'A+',
    projects: ['Microservices Design', 'Cloud Migration Strategy'],
    personality: 'Methodical and inspiring',
    funFact: 'Has a collection of vintage computer manuals',
    color: 'from-blue-500 to-indigo-600',
    particles: ['🏗️', '⚙️', '🔧', '📐']
  },
  {
    id: 'prof-johnson',
    name: 'Prof. <PERSON>',
    subject: 'Network Security & Ethical Hacking',
    avatar: '👩‍💻',
    learned: 'Security isn\'t just about technology - it\'s about understanding human behavior and thinking like an attacker.',
    quote: '"To defend effectively, you must first learn to attack ethically."',
    impact: 'Critical',
    course: 'Cybersecurity Fundamentals',
    semester: 'Spring 2023',
    grade: 'A',
    projects: ['Penetration Testing Lab', 'Security Audit Report'],
    personality: 'Sharp and encouraging',
    funFact: 'Former white-hat hacker who helped secure major banks',
    color: 'from-red-500 to-pink-600',
    particles: ['🔒', '🛡️', '🔍', '⚡']
  },
  {
    id: 'ms-chen',
    name: 'Ms. Lisa Chen',
    subject: 'Database Management & Analytics',
    avatar: '👩‍🔬',
    learned: 'Data tells stories, but you need to know how to listen. Every query is a question waiting for an answer.',
    quote: '"Data without context is just noise. Context without data is just opinion."',
    impact: 'High',
    course: 'Advanced Database Systems',
    semester: 'Fall 2022',
    grade: 'A+',
    projects: ['Data Warehouse Design', 'Real-time Analytics Dashboard'],
    personality: 'Analytical and patient',
    funFact: 'Can write complex SQL queries faster than most people type',
    color: 'from-green-500 to-teal-600',
    particles: ['📊', '🗄️', '📈', '🔍']
  },
  {
    id: 'dr-williams',
    name: 'Dr. Michael Williams',
    subject: 'Software Engineering & Project Management',
    avatar: '👨‍💼',
    learned: 'Great software isn\'t just about code - it\'s about communication, planning, and understanding user needs.',
    quote: '"Code is written once but read a thousand times. Make it count."',
    impact: 'High',
    course: 'Software Project Management',
    semester: 'Spring 2024',
    grade: 'A',
    projects: ['Agile Development Simulation', 'Team Leadership Project'],
    personality: 'Organized and motivational',
    funFact: 'Led development teams at three Fortune 500 companies',
    color: 'from-purple-500 to-violet-600',
    particles: ['📋', '👥', '🚀', '💡']
  },
  {
    id: 'prof-garcia',
    name: 'Prof. Maria Garcia',
    subject: 'Human-Computer Interaction & UX Design',
    avatar: '👩‍🎨',
    learned: 'Technology should adapt to humans, not the other way around. Every interface tells a story about its creators.',
    quote: '"The best user interface is the one that gets out of the user\'s way."',
    impact: 'Medium',
    course: 'User Experience Design',
    semester: 'Fall 2023',
    grade: 'A+',
    projects: ['Mobile App Redesign', 'Accessibility Audit'],
    personality: 'Creative and empathetic',
    funFact: 'Designed interfaces used by millions of people daily',
    color: 'from-orange-500 to-amber-600',
    particles: ['🎨', '✨', '👤', '💫']
  },
  {
    id: 'dr-thompson',
    name: 'Dr. James Thompson',
    subject: 'Artificial Intelligence & Machine Learning',
    avatar: '🤖',
    learned: 'AI isn\'t magic - it\'s math, statistics, and a lot of trial and error. The real intelligence is in asking the right questions.',
    quote: '"AI will not replace humans, but humans with AI will replace humans without AI."',
    impact: 'Critical',
    course: 'Introduction to Machine Learning',
    semester: 'Spring 2024',
    grade: 'A',
    projects: ['Neural Network Implementation', 'Predictive Analytics Model'],
    personality: 'Brilliant and philosophical',
    funFact: 'Published 50+ papers on machine learning algorithms',
    color: 'from-cyan-500 to-blue-600',
    particles: ['🧠', '⚡', '🔮', '🎯']
  }
];

export function ProfessorsGrid() {
  const [selectedProfessor, setSelectedProfessor] = useState<string | null>(null);
  const [discoveredSecrets, setDiscoveredSecrets] = useState<string[]>([]);
  const [hoveredProfessor, setHoveredProfessor] = useState<string | null>(null);

  const handleProfessorClick = (professorId: string) => {
    setSelectedProfessor(selectedProfessor === professorId ? null : professorId);
  };

  const handleSecretReveal = (professorId: string) => {
    if (!discoveredSecrets.includes(professorId)) {
      setDiscoveredSecrets(prev => [...prev, professorId]);
    }
  };

  return (
    <>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {professors.map((professor, index) => (
          <ProfessorCard
            key={professor.id}
            professor={professor}
            index={index}
            isSelected={selectedProfessor === professor.id}
            isHovered={hoveredProfessor === professor.id}
            secretRevealed={discoveredSecrets.includes(professor.id)}
            onClick={() => handleProfessorClick(professor.id)}
            onHover={() => setHoveredProfessor(professor.id)}
            onLeave={() => setHoveredProfessor(null)}
            onSecretReveal={() => handleSecretReveal(professor.id)}
          />
        ))}
      </div>

      {/* Achievement notification */}
      <AnimatePresence>
        {discoveredSecrets.length === professors.length && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -50 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
          >
            <Card className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-none shadow-2xl max-w-md mx-4">
              <CardContent className="p-8 text-center">
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 360, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-6xl mb-4"
                >
                  🎓
                </motion.div>
                <h3 className="text-2xl font-bold mb-4">
                  Academic Achievement Unlocked!
                </h3>
                <p className="mb-4">
                  You've discovered all the secrets about my amazing professors! They've shaped not just my technical skills, but my approach to problem-solving and innovation.
                </p>
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-sm opacity-80"
                >
                  Click anywhere to continue...
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Progress indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="fixed bottom-6 right-6 bg-black/80 backdrop-blur-sm text-green-400 p-3 rounded-lg font-mono text-sm z-40 border border-green-400/30"
      >
        <div className="text-green-300 mb-1">MENTORS DISCOVERED</div>
        <div className="flex space-x-1">
          {professors.map((professor) => (
            <div
              key={professor.id}
              className={`w-3 h-3 rounded-full transition-colors ${
                discoveredSecrets.includes(professor.id) 
                  ? 'bg-green-400' 
                  : 'bg-gray-600'
              }`}
            />
          ))}
        </div>
        <div className="text-xs mt-1 opacity-80">
          {discoveredSecrets.length}/{professors.length} secrets revealed
        </div>
      </motion.div>
    </>
  );
}

interface ProfessorCardProps {
  professor: typeof professors[0];
  index: number;
  isSelected: boolean;
  isHovered: boolean;
  secretRevealed: boolean;
  onClick: () => void;
  onHover: () => void;
  onLeave: () => void;
  onSecretReveal: () => void;
}

function ProfessorCard({ 
  professor, 
  index, 
  isSelected, 
  isHovered,
  secretRevealed,
  onClick, 
  onHover, 
  onLeave,
  onSecretReveal
}: ProfessorCardProps) {
  const [clickCount, setClickCount] = useState(0);

  const handleCardClick = () => {
    setClickCount(prev => prev + 1);
    onClick();
    
    if (clickCount >= 2) {
      onSecretReveal();
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'Medium': return 'text-blue-400';
      case 'High': return 'text-purple-400';
      case 'Critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateX: -15 }}
      animate={{ opacity: 1, y: 0, rotateX: 0 }}
      transition={{ duration: 0.8, delay: index * 0.1 }}
      whileHover={{ y: -5, rotateY: 5 }}
      onHoverStart={onHover}
      onHoverEnd={onLeave}
      className="perspective-1000"
    >
      <Card 
        className={`
          cursor-pointer transition-all duration-300 overflow-hidden h-full
          bg-gradient-to-br ${professor.color}/10 
          border-2 border-transparent
          hover:border-primary/50 hover:shadow-2xl hover:shadow-primary/20
          ${isSelected ? 'ring-2 ring-primary ring-opacity-75' : ''}
          ${secretRevealed ? 'ring-2 ring-yellow-400 ring-opacity-75' : ''}
        `}
        onClick={handleCardClick}
        onDoubleClick={onSecretReveal}
      >
        <CardContent className="p-6 relative">
          {/* Academic background pattern */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden opacity-5">
            <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-10" />
          </div>

          {/* Floating particles */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {professor.particles.map((particle, i) => (
              <motion.div
                key={i}
                className="absolute text-lg opacity-20"
                style={{
                  left: `${15 + i * 20}%`,
                  top: `${10 + i * 15}%`,
                }}
                animate={{
                  y: [0, -15, 0],
                  rotate: [0, 180, 360],
                  opacity: isHovered ? [0.2, 0.5, 0.2] : [0.1, 0.3, 0.1]
                }}
                transition={{
                  duration: 4 + i,
                  repeat: Infinity,
                  delay: i * 0.5
                }}
              >
                {particle}
              </motion.div>
            ))}
          </div>

          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <motion.div
                className="text-4xl"
                animate={isSelected ? {
                  scale: [1, 1.2, 1],
                  rotate: [0, 360, 0]
                } : {}}
                transition={{ duration: 0.5 }}
              >
                {professor.avatar}
              </motion.div>
              
              <div className="text-right">
                <div className={`text-xs px-2 py-1 rounded-full bg-black/20 ${getImpactColor(professor.impact)}`}>
                  {professor.impact} Impact
                </div>
                {clickCount > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="mt-1 text-xs bg-primary text-primary-foreground rounded-full px-2 py-1"
                  >
                    {clickCount} clicks
                  </motion.div>
                )}
              </div>
            </div>

            <h3 className="text-xl font-bold mb-1">
              {professor.name}
            </h3>
            
            <p className="text-sm text-primary/80 mb-3 font-medium">
              {professor.subject}
            </p>

            <div className="space-y-3 text-sm">
              <div>
                <span className="font-semibold text-blue-400">📚 What I learned:</span>
                <p className="text-muted-foreground mt-1">{professor.learned}</p>
              </div>

              <AnimatePresence>
                {isSelected && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    <div>
                      <span className="font-semibold text-purple-400">💬 Favorite quote:</span>
                      <p className="text-muted-foreground mt-1 italic">"{professor.quote}"</p>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="font-semibold">Course:</span>
                        <p className="text-muted-foreground">{professor.course}</p>
                      </div>
                      <div>
                        <span className="font-semibold">Grade:</span>
                        <p className="text-green-400">{professor.grade}</p>
                      </div>
                      <div>
                        <span className="font-semibold">Semester:</span>
                        <p className="text-muted-foreground">{professor.semester}</p>
                      </div>
                      <div>
                        <span className="font-semibold">Personality:</span>
                        <p className="text-muted-foreground">{professor.personality}</p>
                      </div>
                    </div>

                    <div>
                      <span className="font-semibold text-green-400">🚀 Key projects:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {professor.projects.map((project) => (
                          <span key={project} className="text-xs px-2 py-1 bg-primary/20 text-primary rounded-full">
                            {project}
                          </span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              <AnimatePresence>
                {secretRevealed && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 p-3 bg-yellow-400/10 border border-yellow-400/30 rounded-lg"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-400">🤫</span>
                      <span className="text-sm font-medium text-yellow-400">Fun fact:</span>
                    </div>
                    <p className="text-sm mt-1 italic">
                      {professor.funFact}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <div className="mt-4 text-xs text-muted-foreground/60">
              {secretRevealed ? '🔓 Secret revealed!' : 
               clickCount >= 3 ? 'Double-click to reveal fun fact' :
               `Click ${3 - clickCount} more times to unlock`}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
