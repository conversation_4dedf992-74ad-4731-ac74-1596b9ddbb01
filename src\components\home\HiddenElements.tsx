'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

interface HiddenElementsProps {
  mousePosition: { x: number; y: number };
  discoveredElements: string[];
  onDiscoverElement: (elementId: string) => void;
}

const hiddenMessages = [
  { id: 'konami', text: '↑↑↓↓←→←→BA', position: { x: 0.05, y: 0.95 }, trigger: 'konami' },
  { id: 'matrix', text: 'Wake up, Neo...', position: { x: 0.95, y: 0.05 }, trigger: 'hover' },
  { id: 'coffee', text: '☕ Powered by coffee', position: { x: 0.02, y: 0.5 }, trigger: 'click' },
  { id: 'debug', text: 'console.log("You found me!")', position: { x: 0.5, y: 0.02 }, trigger: 'hover' },
  { id: 'easter-egg', text: '🥚 Easter egg unlocked!', position: { x: 0.98, y: 0.98 }, trigger: 'click' }
];

export function HiddenElements({ mousePosition, discoveredElements, onDiscoverElement }: HiddenElementsProps) {
  const [konamiSequence, setKonamiSequence] = useState<string[]>([]);
  const [showGlitch, setShowGlitch] = useState(false);
  
  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

  // Konami code detection
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      setKonamiSequence(prev => {
        const newSequence = [...prev, e.code].slice(-10);
        
        if (newSequence.length === konamiCode.length && 
            newSequence.every((key, index) => key === konamiCode[index])) {
          onDiscoverElement('konami');
          setShowGlitch(true);
          setTimeout(() => setShowGlitch(false), 2000);
          return [];
        }
        
        return newSequence;
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onDiscoverElement]);

  const getElementVisibility = (element: typeof hiddenMessages[0]) => {
    if (discoveredElements.includes(element.id)) return 1;
    
    if (element.trigger === 'hover') {
      const distance = Math.sqrt(
        Math.pow((element.position.x - mousePosition.x), 2) + 
        Math.pow((element.position.y - mousePosition.y), 2)
      );
      return Math.max(0, Math.min(1, (0.1 - distance) / 0.1));
    }
    
    return 0;
  };

  const handleElementClick = (element: typeof hiddenMessages[0]) => {
    if (element.trigger === 'click') {
      onDiscoverElement(element.id);
    }
  };

  return (
    <>
      {/* Hidden messages */}
      {hiddenMessages.map((element) => (
        <motion.div
          key={element.id}
          className="absolute text-xs font-mono text-primary/80 cursor-pointer select-none z-20"
          style={{
            left: `${element.position.x * 100}%`,
            top: `${element.position.y * 100}%`,
            opacity: getElementVisibility(element)
          }}
          onClick={() => handleElementClick(element)}
          whileHover={{ scale: 1.2 }}
          onHoverStart={() => {
            if (element.trigger === 'hover') {
              onDiscoverElement(element.id);
            }
          }}
        >
          {element.text}
        </motion.div>
      ))}

      {/* Glitch effect overlay */}
      <AnimatePresence>
        {showGlitch && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-30"
          >
            <div className="absolute inset-0 bg-red-500/10 mix-blend-multiply" />
            <div className="absolute inset-0 bg-green-500/10 mix-blend-screen" />
            <div className="absolute inset-0 bg-blue-500/10 mix-blend-overlay" />
            
            <motion.div
              animate={{
                x: [0, -5, 5, -2, 2, 0],
                y: [0, 2, -2, 1, -1, 0]
              }}
              transition={{ duration: 0.1, repeat: 20 }}
              className="absolute inset-0 text-white font-mono text-6xl flex items-center justify-center"
            >
              SYSTEM BREACHED
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Secret portal that appears when multiple elements are discovered */}
      <AnimatePresence>
        {discoveredElements.length >= 5 && (
          <motion.div
            initial={{ scale: 0, rotate: 0 }}
            animate={{ scale: 1, rotate: 360 }}
            exit={{ scale: 0 }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40"
            onClick={() => onDiscoverElement('secret-portal')}
          >
            <div className="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 animate-pulse cursor-pointer flex items-center justify-center text-2xl">
              🌀
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating achievement notifications */}
      <AnimatePresence>
        {discoveredElements.slice(-1).map((elementId) => (
          <motion.div
            key={`notification-${elementId}`}
            initial={{ opacity: 0, y: 50, x: 50 }}
            animate={{ opacity: 1, y: 0, x: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-20 right-6 bg-primary/90 text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50"
          >
            <div className="flex items-center space-x-2">
              <span className="text-lg">🎉</span>
              <span className="font-medium">Discovery unlocked!</span>
            </div>
            <div className="text-sm opacity-80">
              {elementId.replace('-', ' ').toUpperCase()}
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Interactive code snippets that reveal on proximity */}
      <motion.div
        className="absolute top-10 left-10 font-mono text-xs text-muted-foreground/30 select-none"
        style={{
          opacity: Math.max(0, Math.min(1, (0.2 - Math.sqrt(
            Math.pow((0.1 - mousePosition.x), 2) + 
            Math.pow((0.1 - mousePosition.y), 2)
          )) / 0.2))
        }}
        onClick={() => onDiscoverElement('code-snippet-1')}
      >
        <div>// TODO: Add more Easter eggs</div>
        <div>const secrets = findHiddenElements();</div>
        <div>if (secrets.length > 10) unlock();</div>
      </motion.div>

      <motion.div
        className="absolute bottom-10 right-10 font-mono text-xs text-muted-foreground/30 select-none"
        style={{
          opacity: Math.max(0, Math.min(1, (0.2 - Math.sqrt(
            Math.pow((0.9 - mousePosition.x), 2) + 
            Math.pow((0.9 - mousePosition.y), 2)
          )) / 0.2))
        }}
        onClick={() => onDiscoverElement('code-snippet-2')}
      >
        <div>/* You're getting warmer... */</div>
        <div>function exploreMore() {'{'}</div>
        <div>  return "Keep looking!";</div>
        <div>{'}'}</div>
      </motion.div>

      {/* Secret binary message */}
      <motion.div
        className="absolute top-1/3 left-1/4 font-mono text-xs text-green-400/20 select-none transform -rotate-45"
        style={{
          opacity: discoveredElements.includes('binary-message') ? 1 : 
            Math.max(0, Math.min(1, (0.15 - Math.sqrt(
              Math.pow((0.25 - mousePosition.x), 2) + 
              Math.pow((0.33 - mousePosition.y), 2)
            )) / 0.15))
        }}
        onClick={() => onDiscoverElement('binary-message')}
      >
        01001000 01100101 01101100 01101100 01101111
      </motion.div>

      {/* Morse code message */}
      <motion.div
        className="absolute bottom-1/3 right-1/4 font-mono text-xs text-blue-400/20 select-none"
        style={{
          opacity: discoveredElements.includes('morse-code') ? 1 : 
            Math.max(0, Math.min(1, (0.15 - Math.sqrt(
              Math.pow((0.75 - mousePosition.x), 2) + 
              Math.pow((0.67 - mousePosition.y), 2)
            )) / 0.15))
        }}
        onClick={() => onDiscoverElement('morse-code')}
      >
        .... .. .-. . / -- .
      </motion.div>
    </>
  );
}
