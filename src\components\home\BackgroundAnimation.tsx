'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  type: 'code' | 'data' | 'neural' | 'paint' | 'secret';
  content: string;
  color: string;
  isClickable?: boolean;
  secretId?: string;
}

interface InteractiveBackgroundProps {
  mousePosition: { x: number; y: number };
  gameMode: boolean;
  onDiscoverElement: (elementId: string) => void;
}

const codeSnippets = [
  'console.log("Hello World")',
  'function solve()',
  'import React',
  'const data = []',
  'if (condition)',
  'return true',
  'async/await',
  'npm install',
  '// Hidden message',
  'sudo rm -rf /',
  'while(true) dream();'
];

const dataFlows = ['→', '↗', '↘', '←', '↙', '↖', '↑', '↓', '⟲', '⟳'];
const neuralNodes = ['●', '◐', '◑', '◒', '◓', '○', '◉', '⬢', '⬡'];
const paintStrokes = ['~', '∼', '≈', '⌒', '⌐', '¬', '∿', '〰', '⩙'];
const secretElements = ['👁', '🔍', '💎', '🗝', '⚡', '🌟', '🔮', '🎯'];

export function InteractiveBackground({ mousePosition, gameMode, onDiscoverElement }: InteractiveBackgroundProps) {
  const [elements, setElements] = useState<FloatingElement[]>([]);
  const [mouseTrail, setMouseTrail] = useState<Array<{ x: number; y: number; id: number }>>([]);

  useEffect(() => {
    const generateElements = () => {
      const newElements: FloatingElement[] = [];

      // Use seeded random for consistent server/client rendering
      const seededRandom = (seed: number) => {
        const x = Math.sin(seed) * 10000;
        return x - Math.floor(x);
      };

      // Generate regular floating elements
      for (let i = 0; i < 40; i++) {
        const types: FloatingElement['type'][] = ['code', 'data', 'neural', 'paint'];
        const type = types[i % types.length];

        let content = '';
        let color = '';

        switch (type) {
          case 'code':
            content = codeSnippets[i % codeSnippets.length];
            color = 'text-blue-400/20';
            break;
          case 'data':
            content = dataFlows[i % dataFlows.length];
            color = 'text-green-400/20';
            break;
          case 'neural':
            content = neuralNodes[i % neuralNodes.length];
            color = 'text-purple-400/20';
            break;
          case 'paint':
            content = paintStrokes[i % paintStrokes.length];
            color = 'text-orange-400/20';
            break;
        }

        newElements.push({
          id: i,
          x: seededRandom(i * 123) * 100,
          y: seededRandom(i * 456) * 100,
          size: seededRandom(i * 789) * 20 + 10,
          type,
          content,
          color
        });
      }

      // Add secret clickable elements
      for (let i = 0; i < 8; i++) {
        newElements.push({
          id: 1000 + i,
          x: seededRandom((i + 100) * 123) * 100,
          y: seededRandom((i + 100) * 456) * 100,
          size: 20,
          type: 'secret',
          content: secretElements[i],
          color: 'text-primary/0 hover:text-primary/80',
          isClickable: true,
          secretId: `secret-${i}`
        });
      }

      setElements(newElements);
    };

    generateElements();
  }, []);

  // Mouse trail effect
  useEffect(() => {
    if (gameMode) {
      const newTrailPoint = {
        x: mousePosition.x * 100,
        y: mousePosition.y * 100,
        id: Date.now()
      };

      setMouseTrail(prev => [...prev.slice(-10), newTrailPoint]);
    }
  }, [mousePosition, gameMode]);

  const handleSecretClick = (element: FloatingElement) => {
    if (element.isClickable && element.secretId) {
      onDiscoverElement(element.secretId);
    }
  };

  const getElementOpacity = (element: FloatingElement) => {
    if (!gameMode) return element.type === 'secret' ? 0 : 1;

    if (element.type === 'secret') {
      const distance = Math.sqrt(
        Math.pow((element.x - mousePosition.x * 100), 2) +
        Math.pow((element.y - mousePosition.y * 100), 2)
      );
      return Math.max(0, Math.min(1, (20 - distance) / 20));
    }

    return 1;
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Dynamic gradient background that responds to mouse */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: gameMode
            ? `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, rgba(99, 102, 241, 0.1) 0%, transparent 50%)`
            : 'linear-gradient(135deg, rgba(0,0,0,0.05) 0%, transparent 100%)'
        }}
      />

      {/* Mouse trail in game mode */}
      {gameMode && mouseTrail.map((point, index) => (
        <motion.div
          key={point.id}
          className="absolute w-2 h-2 bg-primary/30 rounded-full pointer-events-none"
          style={{
            left: `${point.x}%`,
            top: `${point.y}%`,
          }}
          initial={{ scale: 1, opacity: 0.6 }}
          animate={{ scale: 0, opacity: 0 }}
          transition={{ duration: 1 }}
        />
      ))}

      {/* Floating elements */}
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className={`absolute ${element.color} font-mono select-none transition-all duration-300 ${
            element.isClickable ? 'cursor-pointer hover:scale-150' : 'pointer-events-none'
          }`}
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            fontSize: `${element.size}px`,
            opacity: getElementOpacity(element)
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: getElementOpacity(element),
            scale: element.type === 'secret' ? [0.8, 1.2, 1] : [0, 1, 1, 0],
            x: element.type !== 'secret' ? [0, ((element.id * 123) % 100) - 50] : 0,
            y: element.type !== 'secret' ? [0, ((element.id * 456) % 100) - 50] : 0,
            rotate: element.type !== 'secret' ? [0, (element.id * 789) % 360] : 0
          }}
          transition={{
            duration: element.type === 'secret' ? 2 : ((element.id * 111) % 15) + 10,
            repeat: element.type === 'secret' ? Infinity : Infinity,
            repeatType: element.type === 'secret' ? 'reverse' : 'loop',
            ease: 'linear',
            delay: (element.id * 222) % 5
          }}
          onClick={() => handleSecretClick(element)}
          whileHover={element.isClickable ? { scale: 1.5, rotate: 360 } : {}}
        >
          {element.content}
        </motion.div>
      ))}

      {/* Interactive neural network */}
      <svg className="absolute inset-0 w-full h-full opacity-10 pointer-events-none">
        <defs>
          <pattern id="interactive-grid" width="50" height="50" patternUnits="userSpaceOnUse">
            <circle cx="25" cy="25" r="1" fill="currentColor" opacity="0.3"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#interactive-grid)" />

        {/* Dynamic connections that follow mouse */}
        {gameMode && (
          <motion.circle
            cx={`${mousePosition.x * 100}%`}
            cy={`${mousePosition.y * 100}%`}
            r="50"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.3"
            initial={{ scale: 0 }}
            animate={{ scale: [1, 1.5, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        )}

        {/* Animated connecting lines */}
        {Array.from({ length: gameMode ? 10 : 5 }).map((_, i) => {
          const seededRandom = (seed: number) => {
            const x = Math.sin(seed) * 10000;
            return x - Math.floor(x);
          };

          return (
            <motion.line
              key={i}
              x1={`${seededRandom(i * 111) * 100}%`}
              y1={`${seededRandom(i * 222) * 100}%`}
              x2={`${mousePosition.x * 100}%`}
              y2={`${mousePosition.y * 100}%`}
              stroke="currentColor"
              strokeWidth="1"
              opacity={gameMode ? "0.4" : "0.1"}
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{
                duration: seededRandom(i * 333) * 3 + 2,
                repeat: Infinity,
                repeatType: 'reverse',
                ease: 'easeInOut',
                delay: seededRandom(i * 444) * 2
              }}
            />
          );
        })}
      </svg>

      {/* Parallax layers with mouse interaction */}
      <motion.div
        className="absolute inset-0 opacity-5 pointer-events-none"
        animate={{
          y: [0, -20, 0],
          x: gameMode ? mousePosition.x * 10 - 5 : 0
        }}
        transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
      >
        <div className="w-full h-full bg-gradient-to-r from-transparent via-primary/10 to-transparent" />
      </motion.div>
    </div>
  );
}
