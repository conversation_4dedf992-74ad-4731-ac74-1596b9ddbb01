'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PersonaBubbles } from './PersonaBubbles';
import { InteractiveBackground } from './BackgroundAnimation';
import { HiddenElements } from './HiddenElements';
import { GameHUD } from './GameHUD';

export function HeroSection() {
  const [displayText, setDisplayText] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const [gameMode, setGameMode] = useState(false);
  const [discoveredElements, setDiscoveredElements] = useState<string[]>([]);
  const [showHint, setShowHint] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [clickCount, setClickCount] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  const fullText = "Hi, I'm <PERSON><PERSON><PERSON> 👋";
  const secretSequence = [
    { x: 0.1, y: 0.1 }, // Top-left
    { x: 0.9, y: 0.1 }, // Top-right
    { x: 0.5, y: 0.5 }, // Center
    { x: 0.1, y: 0.9 }, // Bottom-left
    { x: 0.9, y: 0.9 }  // Bottom-right
  ];

  // Terminal typing effect
  useEffect(() => {
    let currentIndex = 0;
    const typingInterval = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setDisplayText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        setTimeout(() => {
          setShowCursor(false);
          setShowHint(true);
        }, 1000);
      }
    }, 100);

    return () => clearInterval(typingInterval);
  }, []);

  // Cursor blinking
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);
    return () => clearInterval(cursorInterval);
  }, []);

  // Mouse tracking for interactive elements
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (sectionRef.current) {
        const rect = sectionRef.current.getBoundingClientRect();
        setMousePosition({
          x: (e.clientX - rect.left) / rect.width,
          y: (e.clientY - rect.top) / rect.height
        });
      }
    };

    const section = sectionRef.current;
    if (section) {
      section.addEventListener('mousemove', handleMouseMove);
      return () => section.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  // Secret sequence detection
  const handleSecretClick = (e: React.MouseEvent) => {
    if (!sectionRef.current) return;

    const rect = sectionRef.current.getBoundingClientRect();
    const clickX = (e.clientX - rect.left) / rect.width;
    const clickY = (e.clientY - rect.top) / rect.height;

    const expectedClick = secretSequence[clickCount];
    const tolerance = 0.1;

    if (Math.abs(clickX - expectedClick.x) < tolerance &&
        Math.abs(clickY - expectedClick.y) < tolerance) {
      setClickCount(prev => prev + 1);

      if (clickCount + 1 === secretSequence.length) {
        setGameMode(true);
        setDiscoveredElements(prev => [...prev, 'secret-sequence']);
      }
    } else {
      setClickCount(0);
    }
  };

  const discoverElement = (elementId: string) => {
    setDiscoveredElements(prev =>
      prev.includes(elementId) ? prev : [...prev, elementId]
    );
  };

  return (
    <section
      ref={sectionRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden cursor-crosshair"
      onClick={handleSecretClick}
    >
      <InteractiveBackground
        mousePosition={mousePosition}
        gameMode={gameMode}
        onDiscoverElement={discoverElement}
      />

      <HiddenElements
        mousePosition={mousePosition}
        discoveredElements={discoveredElements}
        onDiscoverElement={discoverElement}
      />

      {gameMode && (
        <GameHUD
          discoveredElements={discoveredElements}
          totalElements={15} // Total hidden elements to find
        />
      )}

      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <motion.h1
            className="text-5xl md:text-7xl font-bold mb-6 relative"
            whileHover={{ scale: 1.05 }}
            onHoverStart={() => discoverElement('title-hover')}
          >
            <span className="font-mono relative">
              {displayText}
              {showCursor && <span className="animate-pulse">|</span>}

              {/* Hidden glitch effect on hover */}
              <motion.span
                className="absolute inset-0 text-red-500 opacity-0"
                animate={discoveredElements.includes('title-hover') ? {
                  opacity: [0, 0.3, 0],
                  x: [0, -2, 2, 0]
                } : {}}
                transition={{ duration: 0.1, repeat: 3 }}
              >
                {displayText}
              </motion.span>
            </span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.5 }}
            className="text-xl md:text-2xl text-muted-foreground mb-12 relative"
            onDoubleClick={() => discoverElement('subtitle-secret')}
          >
            <span className={discoveredElements.includes('subtitle-secret') ? 'line-through' : ''}>
              IT Support Specialist • Automation Enthusiast • Creative Technologist
            </span>

            <AnimatePresence>
              {discoveredElements.includes('subtitle-secret') && (
                <motion.span
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="block text-primary font-bold mt-2"
                >
                  Digital Detective • Code Whisperer • Pixel Alchemist
                </motion.span>
              )}
            </AnimatePresence>
          </motion.p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 2 }}
        >
          <PersonaBubbles
            gameMode={gameMode}
            onDiscoverElement={discoverElement}
            discoveredElements={discoveredElements}
          />
        </motion.div>

        <AnimatePresence>
          {showHint && !gameMode && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mt-16 text-center"
            >
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-sm text-muted-foreground/60 mb-4"
              >
                💡 Hint: Try clicking the corners and center...
              </motion.div>

              <div className="flex justify-center space-x-2">
                {secretSequence.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index < clickCount ? 'bg-primary' : 'bg-muted-foreground/30'
                    }`}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
}
