'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

interface GameHUDProps {
  discoveredElements: string[];
  totalElements: number;
}

export function GameHUD({ discoveredElements, totalElements }: GameHUDProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [score, setScore] = useState(0);
  const [level, setLevel] = useState(1);
  const [showLevelUp, setShowLevelUp] = useState(false);

  // Calculate score and level
  useEffect(() => {
    const newScore = discoveredElements.length * 100;
    const newLevel = Math.floor(discoveredElements.length / 3) + 1;
    
    if (newLevel > level) {
      setShowLevelUp(true);
      setTimeout(() => setShowLevelUp(false), 2000);
    }
    
    setScore(newScore);
    setLevel(newLevel);
  }, [discoveredElements, level]);

  const progress = (discoveredElements.length / totalElements) * 100;

  const achievements = [
    { id: 'first-discovery', name: 'First Steps', description: 'Found your first hidden element', threshold: 1 },
    { id: 'explorer', name: 'Explorer', description: 'Discovered 5 hidden elements', threshold: 5 },
    { id: 'detective', name: 'Digital Detective', description: 'Found 10 hidden elements', threshold: 10 },
    { id: 'master', name: 'Secret Master', description: 'Discovered all hidden elements', threshold: totalElements }
  ];

  const unlockedAchievements = achievements.filter(
    achievement => discoveredElements.length >= achievement.threshold
  );

  return (
    <>
      {/* Main HUD */}
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed top-6 left-6 bg-black/80 backdrop-blur-sm text-green-400 p-4 rounded-lg font-mono text-sm z-50 border border-green-400/30"
      >
        <div className="flex items-center space-x-4 mb-2">
          <div className="flex items-center space-x-2">
            <span className="text-green-300">LEVEL</span>
            <span className="text-white font-bold">{level}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-300">SCORE</span>
            <motion.span 
              key={score}
              initial={{ scale: 1.2, color: '#00ff00' }}
              animate={{ scale: 1, color: '#ffffff' }}
              className="font-bold"
            >
              {score.toLocaleString()}
            </motion.span>
          </div>
        </div>
        
        <div className="mb-2">
          <div className="flex justify-between text-xs mb-1">
            <span>DISCOVERIES</span>
            <span>{discoveredElements.length}/{totalElements}</span>
          </div>
          <div className="w-48 h-2 bg-gray-700 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-green-400 to-blue-400"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-xs text-green-300 hover:text-green-100 transition-colors"
        >
          {showDetails ? '▼ HIDE DETAILS' : '▶ SHOW DETAILS'}
        </button>
      </motion.div>

      {/* Detailed view */}
      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, x: -300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -300 }}
            className="fixed top-32 left-6 bg-black/90 backdrop-blur-sm text-green-400 p-4 rounded-lg font-mono text-xs z-50 border border-green-400/30 max-w-xs"
          >
            <h3 className="text-green-300 font-bold mb-3">DISCOVERED ELEMENTS</h3>
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {discoveredElements.map((element, index) => (
                <motion.div
                  key={element}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-2"
                >
                  <span className="text-green-500">✓</span>
                  <span className="text-white">{element.replace('-', ' ').toUpperCase()}</span>
                </motion.div>
              ))}
            </div>

            <h3 className="text-green-300 font-bold mt-4 mb-2">ACHIEVEMENTS</h3>
            <div className="space-y-1">
              {achievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className={`flex items-center space-x-2 ${
                    unlockedAchievements.includes(achievement) 
                      ? 'text-yellow-400' 
                      : 'text-gray-500'
                  }`}
                >
                  <span>{unlockedAchievements.includes(achievement) ? '🏆' : '🔒'}</span>
                  <span className="text-xs">{achievement.name}</span>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Level up notification */}
      <AnimatePresence>
        {showLevelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-60"
          >
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-8 rounded-lg text-center shadow-2xl border-4 border-yellow-400">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 1, repeat: 2 }}
                className="text-6xl mb-4"
              >
                ⭐
              </motion.div>
              <h2 className="text-3xl font-bold mb-2">LEVEL UP!</h2>
              <p className="text-xl">Level {level}</p>
              <div className="mt-4 text-sm opacity-80">
                Keep exploring to unlock more secrets!
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mini-map showing discovered areas */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="fixed bottom-6 right-6 bg-black/80 backdrop-blur-sm p-3 rounded-lg border border-green-400/30 z-50"
      >
        <div className="text-green-300 text-xs mb-2 font-mono">EXPLORATION MAP</div>
        <div className="w-24 h-16 bg-gray-800 rounded relative">
          {/* Show discovered areas as glowing dots */}
          {discoveredElements.map((element, index) => (
            <motion.div
              key={element}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute w-1 h-1 bg-green-400 rounded-full"
              style={{
                left: `${(index * 23) % 100}%`,
                top: `${(index * 37) % 100}%`,
              }}
            />
          ))}
          
          {/* Scanning line effect */}
          <motion.div
            className="absolute top-0 left-0 w-full h-0.5 bg-green-400/50"
            animate={{ y: [0, 64, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          />
        </div>
      </motion.div>

      {/* Hint system */}
      {discoveredElements.length > 0 && discoveredElements.length < totalElements && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-blue-900/80 backdrop-blur-sm text-blue-100 px-4 py-2 rounded-lg text-sm font-mono z-50 border border-blue-400/30"
        >
          💡 Hint: Try hovering near the edges and corners...
        </motion.div>
      )}

      {/* Completion celebration */}
      <AnimatePresence>
        {discoveredElements.length === totalElements && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              className="bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 text-white p-12 rounded-2xl text-center shadow-2xl"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 360, 0]
                }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-8xl mb-6"
              >
                🎉
              </motion.div>
              <h1 className="text-4xl font-bold mb-4">CONGRATULATIONS!</h1>
              <p className="text-xl mb-6">You've discovered all hidden elements!</p>
              <p className="text-lg opacity-80">
                You are now a certified Digital Detective! 🕵️‍♀️
              </p>
              <div className="mt-8 text-2xl font-bold">
                FINAL SCORE: {score.toLocaleString()}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
