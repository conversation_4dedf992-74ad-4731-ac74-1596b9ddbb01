'use client';

import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { ProjectsGrid } from '@/components/projects/ProjectsGrid';
import { ProjectsFilter } from '@/components/projects/ProjectsFilter';
import { useState } from 'react';

export default function ProjectsPage() {
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  return (
    <>
      <Header />
      <main className="min-h-screen pt-24">
        <div className="container mx-auto px-6 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Skills in Action
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Showcasing technical skills through stories, not just screenshots
            </p>
          </div>
          
          <ProjectsFilter 
            selectedFilter={selectedFilter}
            onFilterChange={setSelectedFilter}
          />
          
          <ProjectsGrid selectedFilter={selectedFilter} />
        </div>
      </main>
      <Footer />
    </>
  );
}
