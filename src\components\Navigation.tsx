'use client';

import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

const navItems = [
  { href: '/', label: 'Home' },
  { href: '/about', label: 'About' },
  { href: '/projects', label: 'Projects' },
  { href: '/professors', label: 'Professors' },
  { href: '/game', label: 'Game' },
  { href: '/contact', label: 'Contact' }
];

interface NavigationProps {
  className?: string;
}

export function Navigation({ className }: NavigationProps) {
  const pathname = usePathname();

  return (
    <nav className={className}>
      <ul className="hidden md:flex items-center gap-2">
        {navItems.map((item) => (
          <li key={item.href}>
            <Link href={item.href}>
              <Button
                variant="ghost"
                className={cn(
                  "text-sm font-medium transition-all duration-200 hover:scale-105 hover:bg-primary/10",
                  pathname === item.href && "bg-primary/20 text-primary"
                )}
              >
                {item.label}
              </Button>
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
}