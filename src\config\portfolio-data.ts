import type { Persona } from '@/types/portfolio';

export const portfolioData: Record<Persona['id'], Persona> = {
  'engineer': {
    id: 'engineer',
    title: 'IT Operations Specialist',
    emoji: '💻',
    color: 'rgba(99, 102, 241, 1)',
    description: 'Passionate about maintaining robust IT infrastructure and providing exceptional technical support. I focus on system reliability, user satisfaction, and continuous improvement of IT operations.',
    inspirations: [
      {
        name: '<PERSON><PERSON>',
        role: 'Creator of Linux',
        image: '/inspirations/linus-torvalds.jpg',
        lessons: [
          'The importance of open-source collaboration in technology',
          'Building systems that are reliable and scalable',
          'Never stop learning and adapting to new technologies',
          'Quality code and documentation make all the difference'
        ]
      },
      {
        name: '<PERSON>',
        role: 'Computer Programming Pioneer',
        image: '/inspirations/grace-hopper.jpg',
        lessons: [
         'Breaking barriers in technology with determination',
         'The power of clear communication in technical fields',
         'Innovation comes from questioning the status quo',
         'Mentoring others amplifies your impact'
        ]
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        role: 'CEO of Microsoft',
        image: '/inspirations/satya-nadella.jpg',
        lessons: [
          'Empathy and growth mindset drive technological innovation',
          'Cloud computing transforms how we deliver IT services',
          'Inclusive leadership creates stronger technical teams',
          'Customer-first approach in IT operations'
        ]
      },
      {
        name: '<PERSON> Hightower',
        role: 'Cloud Native Advocate',
        image: '/inspirations/kelsey-hightower.jpg',
        lessons: [
          'Automation and orchestration are key to modern IT',
          'Sharing knowledge through teaching and speaking',
          'Simplifying complex systems for better operations',
          'Community involvement accelerates professional growth'
        ]
      }
    ],
    experiences: [
      {
        title: 'IT Operations Diploma Program',
        description: 'Currently pursuing comprehensive training in system administration, network management, and IT service delivery. Gaining hands-on experience with Windows Server, Active Directory, virtualization technologies, and ITIL frameworks. Developing skills in troubleshooting, incident management, and user support.',
        date: '2024 - Present',
        tags: ['Windows Server', 'Active Directory', 'ITIL', 'Networking', 'Virtualization'],
        link: 'https://www.rrc.ca'
      },
      {
        title: 'Help Desk Support Practicum',
        description: 'Provided technical support to end users, resolving hardware and software issues. Documented incidents using ticketing systems, escalated complex problems appropriately, and maintained high customer satisfaction scores. Gained experience with remote support tools and troubleshooting methodologies.',
        date: '2024',
        tags: ['Help Desk', 'Ticketing Systems', 'Remote Support', 'Troubleshooting', 'Customer Service'],
        link: ''
      },
      {
        title: 'Network Configuration Lab Project',
        description: 'Designed and implemented a small business network infrastructure including VLAN configuration, router setup, and security policies. Documented network topology and created maintenance procedures. Demonstrated understanding of TCP/IP, subnetting, and network security principles.',
        date: '2024',
        tags: ['Networking', 'VLANs', 'Router Configuration', 'Network Security', 'Documentation'],
        link: ''
      },
      {
        title: 'System Administration Capstone',
        description: 'Deployed and managed Windows Server environment with Active Directory, Group Policy, and file sharing services. Implemented backup and recovery procedures, monitored system performance, and created user training materials. Project showcased enterprise-level system management skills.',
        date: '2024',
        tags: ['Windows Server', 'Active Directory', 'Group Policy', 'Backup & Recovery', 'Performance Monitoring'],
        link: ''
      },
      {
        title: 'IT Service Management Study',
        description: 'Completed comprehensive study of ITIL framework and IT service management best practices. Learned incident, problem, and change management processes. Developed understanding of service level agreements and continuous service improvement methodologies.',
        date: '2024',
        tags: ['ITIL', 'Service Management', 'Incident Management', 'Change Management', 'SLA'],
        link: ''
      },
      {
        title: 'Cybersecurity Fundamentals',
        description: 'Gained foundational knowledge in cybersecurity principles, threat identification, and security best practices. Learned about firewalls, antivirus management, security policies, and user awareness training. Understanding of compliance requirements and risk assessment.',
        date: '2024',
        tags: ['Cybersecurity', 'Risk Assessment', 'Security Policies', 'Compliance', 'Threat Analysis'],
        link: ''
      },
      {
        title: 'Cloud Services Introduction',
        description: 'Explored cloud computing concepts including Infrastructure as a Service (IaaS), Platform as a Service (PaaS), and Software as a Service (SaaS). Gained hands-on experience with Microsoft Azure and AWS basics, understanding cloud migration and management principles.',
        date: '2024',
        tags: ['Cloud Computing', 'Microsoft Azure', 'AWS', 'IaaS', 'Cloud Migration'],
        link: ''
      }
    ]
  },
  'educator': {
    id: 'educator',
    title: 'Knowledge Facilitator',
    emoji: '🤝',
    color: 'rgba(139, 92, 246, 1)',
    description: 'Dedicated to sharing knowledge and helping others grow in their technical journey. I believe in making complex IT concepts accessible and fostering collaborative learning environments.',
    inspirations: [
      {
        name: 'Sal Khan',
        role: 'Founder of Khan Academy',
        image: '/inspirations/sal-khan.jpg',
        lessons: [
          'Free, accessible education can transform lives',
          'Breaking down complex topics into digestible parts',
          'Technology can scale personalized learning',
          'Patience and persistence in teaching are essential'
        ]
      },
      {
        name: 'Reshma Saujani',
        role: 'Founder of Girls Who Code',
        image: '/inspirations/reshma-saujani.jpg',
        lessons: [
          'Diversity in tech starts with education and mentorship',
          'Creating safe spaces for learning and growth',
          'Advocacy and education go hand in hand',
          'Empowering others multiplies your impact'
        ]
      },
      {
        name: 'Quincy Larson',
        role: 'Founder of freeCodeCamp',
        image: '/inspirations/quincy-larson.jpg',
        lessons: [
              'Open-source education democratizes learning',
              'Community-driven learning is incredibly powerful',
              'Practical, project-based learning works best',
              'Building while learning accelerates skill development'
        ]
      }
    ],
    experiences: [
      {
        title: 'Peer Tutoring in IT Fundamentals',
        description: 'Provided one-on-one and group tutoring sessions for fellow students struggling with networking concepts, system administration basics, and troubleshooting methodologies. Developed simplified explanations and hands-on exercises to improve understanding and retention.',
        date: '2024',
        tags: ['Tutoring', 'Networking', 'System Administration', 'Mentoring'],
        link: ''
      },
      {
        title: 'Technical Documentation Creation',
        description: 'Created comprehensive study guides and step-by-step tutorials for common IT procedures including Active Directory setup, network configuration, and troubleshooting workflows. Materials were adopted by instructors and shared with multiple cohorts.',
        date: '2024',
        tags: ['Documentation', 'Technical Writing', 'Knowledge Sharing', 'Process Documentation'],
        link: ''
      },
      {
        title: 'Study Group Leadership',
        description: 'Organized and led weekly study groups for IT Operations students, facilitating collaborative learning sessions focused on exam preparation and practical skill development. Improved group performance and fostered supportive learning environment.',
        date: '2024',
        tags: ['Leadership', 'Group Facilitation', 'Collaborative Learning', 'Exam Preparation'],
        link: ''
      },
      {
        title: 'New Student Orientation Assistant',
        description: 'Assisted new IT program students with orientation activities, provided guidance on course expectations, and shared study strategies. Helped create welcoming environment and reduced first-semester anxiety for incoming students.',
        date: '2024',
        tags: ['Mentoring', 'Student Support', 'Orientation', 'Communication'],
        link: ''
      },
      {
        title: 'IT Skills Workshop Presenter',
        description: 'Presented workshops on basic computer troubleshooting and digital literacy skills for community members. Adapted technical content for non-technical audiences and received positive feedback for clear, patient instruction style.',
        date: '2024',
        tags: ['Public Speaking', 'Workshop Facilitation', 'Digital Literacy', 'Community Outreach'],
        link: ''
      }
    ]
  },
  'movement-builder': {
    id: 'movement-builder',
    title: 'Community Connector',
    emoji: '🎨',
    color: 'rgba(236, 72, 153, 1)',
    description: 'Committed to building inclusive tech communities and bridging the gap between technical expertise and meaningful social impact. I believe technology should serve everyone.',
    inspirations: [
      {
        name: 'Kimberly Bryant',
        role: 'Founder of Black Girls CODE',
        image: '/inspirations/kimberly-bryant.jpg',
        lessons: [
          'Representation matters in technology fields',
          'Early exposure to tech can change life trajectories',
          'Community programs create lasting impact',
          'Persistence in advocacy drives real change'
        ]
      },
      {
        name: 'Tarana Burke',
        role: 'Civil Rights Activist',
        image: '/inspirations/tarana-burke.jpg',
        lessons: [
            'Grassroots movements can create global change',
            'Technology amplifies voices and movements',
            'Healing and empowerment go hand in hand',
            'Community support is essential for growth'
        ]
      },
      {
        name: 'Leah Culver',
        role: 'Tech Entrepreneur & Advocate',
        image: '/inspirations/leah-culver.jpg',
        lessons: [
            'Building inclusive tech products from the start',
            'Mentorship and sponsorship create opportunities',
            'Diverse teams build better solutions',
            'Using privilege to lift others up'
        ]
      },
      {
        name: 'Ayana Elizabeth Johnson',
        role: 'Climate Policy Expert',
        image: '/inspirations/ayana-johnson.jpg',
        lessons: [
            'Technology can address global challenges',
            'Interdisciplinary collaboration drives innovation',
            'Communication makes complex issues accessible',
            'Optimism and action can coexist with realism'
        ]
      }
    ],
    experiences: [
      {
        title: 'Volunteer Graphic Designer',
        description: 'Created visual materials for local community organizations including event flyers, social media graphics, and promotional materials. Collaborated with non-profit teams to develop cohesive brand messaging and increase community engagement.',
        date: '2023-2024',
        tags: ['Graphic Design', 'Community Service', 'Brand Development', 'Social Media'],
        link: ''
      },
      {
        title: 'IT Accessibility Advocate',
        description: 'Researched and promoted accessible technology solutions for community members with disabilities. Organized workshops on assistive technologies and advocated for inclusive design principles in local tech initiatives.',
        date: '2024',
        tags: ['Accessibility', 'Assistive Technology', 'Advocacy', 'Inclusive Design'],
        link: ''
      },
      {
        title: 'Digital Divide Bridge Builder',
        description: 'Volunteered to help seniors and newcomers develop basic digital literacy skills. Provided patient, culturally-sensitive instruction on essential technology use including email, online banking, and telehealth services.',
        date: '2023-2024',
        tags: ['Digital Literacy', 'Community Outreach', 'Cultural Sensitivity', 'Volunteer Training'],
        link: ''
      },
      {
        title: 'Tech Career Mentorship Program',
        description: 'Participated in mentorship program connecting IT students with underrepresented communities. Shared career insights, provided resume feedback, and helped mentees navigate educational and professional opportunities in technology.',
        date: '2024',
        tags: ['Mentorship', 'Career Guidance', 'Diversity & Inclusion', 'Professional Development'],
        link: ''
      },
      {
        title: 'Sustainable Tech Initiative',
        description: 'Organized e-waste collection drives and promoted responsible technology disposal practices. Educated community members about environmental impact of technology and advocated for sustainable IT practices in local organizations.',
        date: '2024',
        tags: ['Environmental Sustainability', 'E-waste Management', 'Community Education', 'Green IT'],
        link: ''
      },
      {
        title: 'Newcomer Tech Support Volunteer',
        description: 'Provided technology support and guidance to newcomers to Canada, helping them navigate digital government services, online job applications, and essential technology tools. Offered multilingual support and cultural bridge-building.',
        date: '2023-2024',
        tags: ['Newcomer Support', 'Multilingual Communication', 'Government Services', 'Cultural Competency'],
        link: ''
      }
    ]
  }
}; 
