'use client';

import { motion, useInView, AnimatePresence } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';

const timelineEvents = [
  {
    id: 'education-start',
    year: '2023',
    title: 'Starting My IT Journey',
    emoji: '🎓',
    description: 'Enrolled in the IT Operations Diploma program at Red River College to build a solid foundation in technology.',
    details: 'Coming from a different background, I was excited to dive into system administration, networking, and technical support.',
    color: 'from-blue-500 to-cyan-500',
    particles: ['📚', '💻', '🔍', '💡'],
    secretMessage: 'I was nervous but excited about my first networking lab',
    interactiveElements: {
      clickable: true,
      hoverEffect: 'glow',
      soundEffect: 'beep'
    }
  },
  {
    id: 'first-success',
    year: '2024',
    title: 'First Major IT Success',
    emoji: '🔧',
    description: 'Successfully configured my first Active Directory environment and felt the satisfaction of solving complex technical problems.',
    details: 'The moment when all the domain controllers, group policies, and user accounts worked together perfectly was incredibly rewarding.',
    color: 'from-green-500 to-emerald-500',
    particles: ['🔧', '⚙️', '🖥️', '📡'],
    secretMessage: 'I celebrated by doing a little happy dance in the computer lab',
    interactiveElements: {
      clickable: true,
      hoverEffect: 'rotate',
      soundEffect: 'click'
    }
  },
  {
    id: 'helping-others',
    year: '2024',
    title: 'Discovering My Passion for Helping Others',
    emoji: '🤝',
    description: 'Started tutoring fellow students and realized I love making complex IT concepts accessible to others.',
    details: 'Whether explaining subnetting or troubleshooting Windows issues, I found joy in helping others succeed.',
    color: 'from-purple-500 to-pink-500',
    particles: ['🤝', '✨', '📖', '💫'],
    secretMessage: 'My study group nicknamed me "The IT Whisperer"',
    interactiveElements: {
      clickable: true,
      hoverEffect: 'rainbow',
      soundEffect: 'chime'
    }
  },
  {
    id: 'future',
    year: '2024+',
    title: 'Where I\'m Going Next',
    emoji: '🚀',
    description: 'Building the future with AI, automation, and innovative solutions that make a difference.',
    details: 'Aspiring to lead in data science, create intelligent automation systems, and maybe start my own tech company.',
    color: 'from-orange-500 to-red-500',
    particles: ['🚀', '🌟', '⚡', '🔮'],
    secretMessage: 'My dream is to build AI that helps solve climate change',
    interactiveElements: {
      clickable: true,
      hoverEffect: 'launch',
      soundEffect: 'whoosh'
    }
  }
];

export function AboutTimeline() {
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [discoveredSecrets, setDiscoveredSecrets] = useState<string[]>([]);
  const [animationTrigger, setAnimationTrigger] = useState<string | null>(null);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const handleEventClick = (eventId: string) => {
    setSelectedEvent(selectedEvent === eventId ? null : eventId);
    setAnimationTrigger(eventId);
    
    // Discover secret after 3 clicks
    const clickCount = discoveredSecrets.filter(s => s.startsWith(eventId)).length;
    if (clickCount < 3) {
      setDiscoveredSecrets(prev => [...prev, `${eventId}-click-${clickCount + 1}`]);
    }
    
    setTimeout(() => setAnimationTrigger(null), 1000);
  };

  const handleSecretReveal = (eventId: string) => {
    if (!discoveredSecrets.includes(`${eventId}-secret`)) {
      setDiscoveredSecrets(prev => [...prev, `${eventId}-secret`]);
    }
  };

  return (
    <section ref={ref} className="relative">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{ duration: 0.8 }}
        className="text-center mb-12"
      >
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
          My Tech Journey
        </h2>
        <p className="text-xl text-muted-foreground">
          Click on each milestone to discover the story behind it
        </p>
      </motion.div>

      {/* Animated timeline line */}
      <div className="relative">
        <motion.div
          className="absolute left-8 top-0 w-1 bg-gradient-to-b from-blue-500 via-green-500 via-purple-500 to-orange-500 rounded-full"
          initial={{ height: 0 }}
          animate={isInView ? { height: '100%' } : { height: 0 }}
          transition={{ duration: 2, delay: 0.5 }}
        />

        <div className="space-y-12">
          {timelineEvents.map((event, index) => (
            <TimelineEvent
              key={event.id}
              event={event}
              index={index}
              isInView={isInView}
              isSelected={selectedEvent === event.id}
              isAnimating={animationTrigger === event.id}
              discoveredSecrets={discoveredSecrets}
              onClick={() => handleEventClick(event.id)}
              onSecretReveal={() => handleSecretReveal(event.id)}
            />
          ))}
        </div>
      </div>

      {/* Floating achievement notifications */}
      <AnimatePresence>
        {discoveredSecrets.slice(-1).map((secret) => (
          <motion.div
            key={secret}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: -300, scale: 0.8 }}
            className="fixed top-20 right-6 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-3 rounded-lg shadow-2xl z-50 border border-white/20"
          >
            <div className="flex items-center space-x-2">
              <motion.span 
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 1 }}
                className="text-lg"
              >
                🎉
              </motion.span>
              <div>
                <div className="font-bold">Secret Discovered!</div>
                <div className="text-sm opacity-90">Timeline milestone unlocked</div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Progress indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isInView ? 1 : 0 }}
        className="fixed bottom-6 left-6 bg-black/80 backdrop-blur-sm text-green-400 p-3 rounded-lg font-mono text-sm z-40 border border-green-400/30"
      >
        <div className="text-green-300 mb-1">TIMELINE PROGRESS</div>
        <div className="flex space-x-1">
          {timelineEvents.map((event, index) => (
            <div
              key={event.id}
              className={`w-3 h-3 rounded-full transition-colors ${
                discoveredSecrets.some(s => s.startsWith(event.id)) 
                  ? 'bg-green-400' 
                  : 'bg-gray-600'
              }`}
            />
          ))}
        </div>
        <div className="text-xs mt-1 opacity-80">
          {discoveredSecrets.length}/{timelineEvents.length * 3} secrets found
        </div>
      </motion.div>
    </section>
  );
}

interface TimelineEventProps {
  event: typeof timelineEvents[0];
  index: number;
  isInView: boolean;
  isSelected: boolean;
  isAnimating: boolean;
  discoveredSecrets: string[];
  onClick: () => void;
  onSecretReveal: () => void;
}

function TimelineEvent({ 
  event, 
  index, 
  isInView, 
  isSelected, 
  isAnimating,
  discoveredSecrets,
  onClick,
  onSecretReveal
}: TimelineEventProps) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      setMousePosition({
        x: (e.clientX - rect.left) / rect.width,
        y: (e.clientY - rect.top) / rect.height
      });
    }
  };

  const secretRevealed = discoveredSecrets.includes(`${event.id}-secret`);
  const clickCount = discoveredSecrets.filter(s => s.startsWith(event.id)).length;

  return (
    <motion.div
      initial={{ opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'} relative`}
    >
      {/* Timeline dot */}
      <motion.div
        className={`absolute left-8 w-6 h-6 rounded-full bg-gradient-to-r ${event.color} border-4 border-background shadow-lg z-10`}
        whileHover={{ scale: 1.5 }}
        animate={isAnimating ? { 
          scale: [1, 1.5, 1],
          rotate: [0, 360, 0]
        } : {}}
        style={{ transform: 'translateX(-50%)' }}
      />

      {/* Event card */}
      <motion.div
        ref={cardRef}
        className={`ml-20 ${index % 2 === 0 ? 'mr-8' : 'ml-8 mr-20'} flex-1`}
        onMouseMove={handleMouseMove}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Card 
          className={`
            cursor-pointer transition-all duration-300 overflow-hidden
            bg-gradient-to-br ${event.color}/10 
            border-2 border-transparent
            hover:border-primary/50 hover:shadow-2xl hover:shadow-primary/20
            ${isSelected ? 'ring-2 ring-primary ring-opacity-75' : ''}
            ${secretRevealed ? 'ring-2 ring-yellow-400 ring-opacity-75' : ''}
          `}
          onClick={onClick}
          onDoubleClick={onSecretReveal}
        >
          <CardContent className="p-6 relative">
            {/* Floating particles */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {event.particles.map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute text-lg opacity-20"
                  style={{
                    left: `${20 + i * 20}%`,
                    top: `${10 + i * 15}%`,
                  }}
                  animate={{
                    y: [0, -20, 0],
                    x: [0, 10, 0],
                    rotate: [0, 180, 360],
                    opacity: isSelected ? [0.2, 0.6, 0.2] : [0.1, 0.3, 0.1]
                  }}
                  transition={{
                    duration: 4 + i,
                    repeat: Infinity,
                    delay: i * 0.5
                  }}
                >
                  {particle}
                </motion.div>
              ))}
            </div>

            {/* Interactive glow effect */}
            <motion.div
              className="absolute inset-0 opacity-0 bg-gradient-to-r from-primary/20 to-purple-600/20 rounded-lg"
              animate={{
                opacity: isSelected ? 0.3 : 0,
                scale: isSelected ? 1.05 : 1
              }}
              style={{
                background: `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, rgba(99, 102, 241, 0.2) 0%, transparent 50%)`
              }}
            />

            <div className="relative z-10">
              <div className="flex items-center mb-4">
                <motion.div
                  className="text-3xl mr-4"
                  animate={isAnimating ? {
                    scale: [1, 1.5, 1],
                    rotate: [0, 360, 0]
                  } : {}}
                >
                  {event.emoji}
                </motion.div>
                
                <div>
                  <div className="text-sm font-mono text-primary/80 mb-1">
                    {event.year}
                  </div>
                  <h3 className="text-xl font-bold">
                    {event.title}
                  </h3>
                </div>

                {/* Click counter */}
                {clickCount > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="ml-auto bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold"
                  >
                    {clickCount}
                  </motion.div>
                )}
              </div>

              <p className="text-muted-foreground mb-4">
                {event.description}
              </p>

              <AnimatePresence>
                {isSelected && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-t border-border pt-4"
                  >
                    <p className="text-sm">
                      {event.details}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>

              <AnimatePresence>
                {secretRevealed && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 p-3 bg-yellow-400/10 border border-yellow-400/30 rounded-lg"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-400">🤫</span>
                      <span className="text-sm font-medium text-yellow-400">Secret:</span>
                    </div>
                    <p className="text-sm mt-1 italic">
                      {event.secretMessage}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>

              <div className="mt-4 text-xs text-muted-foreground/60">
                {secretRevealed ? '🔓 Secret revealed!' : 
                 clickCount >= 3 ? 'Double-click to reveal secret' :
                 `Click ${3 - clickCount} more times to unlock`}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
