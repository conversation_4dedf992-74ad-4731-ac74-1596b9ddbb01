'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';

const filters = [
  { id: 'all', label: 'All Projects', emoji: '🌟', color: 'from-blue-500 to-purple-500' },
  { id: 'scripting', label: 'Scripting', emoji: '📜', color: 'from-green-500 to-teal-500' },
  { id: 'ux-design', label: 'UX Design', emoji: '🎨', color: 'from-pink-500 to-rose-500' },
  { id: 'networking', label: 'Networking', emoji: '🌐', color: 'from-cyan-500 to-blue-500' },
  { id: 'automation', label: 'Automation', emoji: '🤖', color: 'from-purple-500 to-indigo-500' },
  { id: 'security', label: 'Security', emoji: '🔒', color: 'from-red-500 to-orange-500' }
];

interface ProjectsFilterProps {
  selectedFilter: string;
  onFilterChange: (filter: string) => void;
}

export function ProjectsFilter({ selectedFilter, onFilterChange }: ProjectsFilterProps) {
  return (
    <div className="flex flex-wrap justify-center gap-3 mb-12">
      {filters.map((filter, index) => (
        <motion.div
          key={filter.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            variant={selectedFilter === filter.id ? "default" : "outline"}
            className={`
              relative overflow-hidden transition-all duration-300
              ${selectedFilter === filter.id 
                ? `bg-gradient-to-r ${filter.color} text-white border-none shadow-lg` 
                : 'hover:border-primary/50'
              }
            `}
            onClick={() => onFilterChange(filter.id)}
          >
            {/* Animated background for selected filter */}
            {selectedFilter === filter.id && (
              <motion.div
                className="absolute inset-0 bg-white/20"
                animate={{
                  x: ['-100%', '100%'],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              />
            )}
            
            <span className="relative z-10 flex items-center space-x-2">
              <motion.span
                animate={selectedFilter === filter.id ? {
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                } : {}}
                transition={{ duration: 0.5 }}
              >
                {filter.emoji}
              </motion.span>
              <span>{filter.label}</span>
            </span>
            
            {/* Particle effect for active filter */}
            {selectedFilter === filter.id && (
              <div className="absolute inset-0 pointer-events-none">
                {Array.from({ length: 3 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-white rounded-full"
                    style={{
                      left: `${20 + i * 30}%`,
                      top: `${20 + i * 20}%`,
                    }}
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2
                    }}
                  />
                ))}
              </div>
            )}
          </Button>
        </motion.div>
      ))}
    </div>
  );
}
