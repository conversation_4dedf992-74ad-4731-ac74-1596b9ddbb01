'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ExternalLink, Github } from 'lucide-react';

const projects = [
  {
    id: 'network-automation',
    title: 'Network Automation Suite',
    category: 'automation',
    problem: 'Manual network configuration was taking hours and prone to human error',
    solution: 'Built Python scripts with Ansible to automate router/switch configurations',
    result: 'Reduced deployment time by 80% and eliminated configuration errors',
    tags: ['Python', 'Ansible', 'Networking', 'Automation'],
    emoji: '🌐',
    color: 'from-blue-500 to-cyan-500',
    github: 'https://github.com/ghazal/network-automation',
    demo: 'https://demo.network-automation.com',
    difficulty: 'Advanced',
    timeSpent: '3 months',
    impact: 'High'
  },
  {
    id: 'siem-dashboard',
    title: 'SIEM Security Dashboard',
    category: 'security',
    problem: 'Security alerts were scattered across multiple systems',
    solution: 'Created unified dashboard using ELK stack with real-time monitoring',
    result: 'Improved threat detection response time by 60%',
    tags: ['ElasticSearch', 'Kibana', 'Security', 'Dashboard'],
    emoji: '🔒',
    color: 'from-red-500 to-orange-500',
    github: 'https://github.com/ghazal/siem-dashboard',
    demo: 'https://demo.siem-dashboard.com',
    difficulty: 'Expert',
    timeSpent: '4 months',
    impact: 'Critical'
  },
  {
    id: 'ui-component-library',
    title: 'Accessible UI Component Library',
    category: 'ux-design',
    problem: 'Inconsistent UI components across different projects',
    solution: 'Designed and built reusable component library with accessibility focus',
    result: 'Increased development speed by 40% and improved accessibility scores',
    tags: ['React', 'TypeScript', 'Figma', 'Accessibility'],
    emoji: '🎨',
    color: 'from-pink-500 to-purple-500',
    github: 'https://github.com/ghazal/ui-library',
    demo: 'https://storybook.ui-library.com',
    difficulty: 'Intermediate',
    timeSpent: '2 months',
    impact: 'Medium'
  },
  {
    id: 'powershell-toolkit',
    title: 'PowerShell Admin Toolkit',
    category: 'scripting',
    problem: 'Repetitive Windows administration tasks consuming too much time',
    solution: 'Developed comprehensive PowerShell module for common admin tasks',
    result: 'Automated 90% of routine tasks, saving 20 hours per week',
    tags: ['PowerShell', 'Windows', 'Administration', 'Automation'],
    emoji: '📜',
    color: 'from-green-500 to-teal-500',
    github: 'https://github.com/ghazal/powershell-toolkit',
    difficulty: 'Intermediate',
    timeSpent: '6 weeks',
    impact: 'High'
  },
  {
    id: 'network-monitor',
    title: 'Real-time Network Monitor',
    category: 'networking',
    problem: 'Network issues were discovered too late, causing downtime',
    solution: 'Built monitoring system with SNMP and custom alerting',
    result: 'Reduced network downtime by 75% with proactive monitoring',
    tags: ['SNMP', 'Python', 'Monitoring', 'Alerts'],
    emoji: '📡',
    color: 'from-cyan-500 to-blue-500',
    github: 'https://github.com/ghazal/network-monitor',
    demo: 'https://demo.network-monitor.com',
    difficulty: 'Advanced',
    timeSpent: '10 weeks',
    impact: 'Critical'
  },
  {
    id: 'backup-orchestrator',
    title: 'Intelligent Backup Orchestrator',
    category: 'automation',
    problem: 'Backup processes were unreliable and not properly monitored',
    solution: 'Created smart backup system with failure detection and auto-recovery',
    result: 'Achieved 99.9% backup success rate with automated error handling',
    tags: ['Python', 'Cron', 'Monitoring', 'Recovery'],
    emoji: '💾',
    color: 'from-purple-500 to-indigo-500',
    github: 'https://github.com/ghazal/backup-orchestrator',
    difficulty: 'Advanced',
    timeSpent: '8 weeks',
    impact: 'Critical'
  }
];

interface ProjectsGridProps {
  selectedFilter: string;
}

export function ProjectsGrid({ selectedFilter }: ProjectsGridProps) {
  const [expandedProject, setExpandedProject] = useState<string | null>(null);
  const [hoveredProject, setHoveredProject] = useState<string | null>(null);

  const filteredProjects = selectedFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedFilter);

  const handleProjectClick = (projectId: string) => {
    setExpandedProject(expandedProject === projectId ? null : projectId);
  };

  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <AnimatePresence mode="popLayout">
        {filteredProjects.map((project, index) => (
          <ProjectCard
            key={project.id}
            project={project}
            index={index}
            isExpanded={expandedProject === project.id}
            isHovered={hoveredProject === project.id}
            onClick={() => handleProjectClick(project.id)}
            onHover={() => setHoveredProject(project.id)}
            onLeave={() => setHoveredProject(null)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

interface ProjectCardProps {
  project: typeof projects[0];
  index: number;
  isExpanded: boolean;
  isHovered: boolean;
  onClick: () => void;
  onHover: () => void;
  onLeave: () => void;
}

function ProjectCard({ 
  project, 
  index, 
  isExpanded, 
  isHovered, 
  onClick, 
  onHover, 
  onLeave 
}: ProjectCardProps) {
  const [clickCount, setClickCount] = useState(0);
  const [showParticles, setShowParticles] = useState(false);

  const handleCardClick = () => {
    setClickCount(prev => prev + 1);
    onClick();
    
    if (clickCount > 0 && clickCount % 3 === 0) {
      setShowParticles(true);
      setTimeout(() => setShowParticles(false), 2000);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-orange-400';
      case 'Expert': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'Low': return 'text-gray-400';
      case 'Medium': return 'text-blue-400';
      case 'High': return 'text-purple-400';
      case 'Critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -5 }}
      onHoverStart={onHover}
      onHoverEnd={onLeave}
      className="relative"
    >
      <Card 
        className={`
          cursor-pointer transition-all duration-300 overflow-hidden h-full
          bg-gradient-to-br ${project.color}/10 
          border-2 border-transparent
          hover:border-primary/50 hover:shadow-2xl hover:shadow-primary/20
          ${isExpanded ? 'ring-2 ring-primary ring-opacity-75' : ''}
        `}
        onClick={handleCardClick}
      >
        <CardContent className="p-6 relative">
          {/* Floating particles */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {project.tags.slice(0, 3).map((tag, i) => (
              <motion.div
                key={i}
                className="absolute text-xs opacity-10 font-mono"
                style={{
                  left: `${20 + i * 25}%`,
                  top: `${10 + i * 20}%`,
                }}
                animate={{
                  y: [0, -15, 0],
                  opacity: isHovered ? [0.1, 0.3, 0.1] : [0.05, 0.15, 0.05]
                }}
                transition={{
                  duration: 3 + i,
                  repeat: Infinity,
                  delay: i * 0.5
                }}
              >
                {tag}
              </motion.div>
            ))}
          </div>

          {/* Particle explosion effect */}
          <AnimatePresence>
            {showParticles && (
              <div className="absolute inset-0 pointer-events-none">
                {Array.from({ length: 8 }).map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ 
                      scale: 0, 
                      x: 0, 
                      y: 0, 
                      opacity: 1 
                    }}
                    animate={{
                      scale: [0, 1, 0],
                      x: (((i * 123) % 100) - 50) * 2,
                      y: (((i * 456) % 100) - 50) * 2,
                      opacity: [1, 1, 0]
                    }}
                    transition={{ 
                      duration: 1.5,
                      delay: i * 0.1,
                      ease: "easeOut"
                    }}
                    className="absolute top-1/2 left-1/2 text-lg"
                  >
                    {project.emoji}
                  </motion.div>
                ))}
              </div>
            )}
          </AnimatePresence>

          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <motion.div
                className="text-3xl"
                animate={isExpanded ? {
                  scale: [1, 1.2, 1],
                  rotate: [0, 360, 0]
                } : {}}
                transition={{ duration: 0.5 }}
              >
                {project.emoji}
              </motion.div>
              
              <div className="flex space-x-1">
                <div className={`text-xs px-2 py-1 rounded-full bg-black/20 ${getDifficultyColor(project.difficulty)}`}>
                  {project.difficulty}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full bg-black/20 ${getImpactColor(project.impact)}`}>
                  {project.impact}
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors">
              {project.title}
            </h3>

            <div className="space-y-3 text-sm">
              <div>
                <span className="font-semibold text-red-400">🔧 Problem:</span>
                <p className="text-muted-foreground mt-1">{project.problem}</p>
              </div>

              <div>
                <span className="font-semibold text-blue-400">🛠️ Solution:</span>
                <p className="text-muted-foreground mt-1">{project.solution}</p>
              </div>

              <div>
                <span className="font-semibold text-green-400">✅ Result:</span>
                <p className="text-muted-foreground mt-1">{project.result}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mt-4">
              {project.tags.map((tag, i) => (
                <motion.span
                  key={tag}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: i * 0.1 }}
                  whileHover={{ scale: 1.1 }}
                  className="text-xs px-2 py-1 bg-primary/20 text-primary rounded-full"
                >
                  {tag}
                </motion.span>
              ))}
            </div>

            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 pt-4 border-t border-border"
                >
                  <div className="grid grid-cols-2 gap-4 text-xs mb-4">
                    <div>
                      <span className="font-semibold">Time Spent:</span>
                      <p className="text-muted-foreground">{project.timeSpent}</p>
                    </div>
                    <div>
                      <span className="font-semibold">Impact:</span>
                      <p className={getImpactColor(project.impact)}>{project.impact}</p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    {project.github && (
                      <Button size="sm" variant="outline" asChild>
                        <a href={project.github} target="_blank" rel="noopener noreferrer">
                          <Github className="w-4 h-4 mr-2" />
                          Code
                        </a>
                      </Button>
                    )}
                    {project.demo && (
                      <Button size="sm" variant="outline" asChild>
                        <a href={project.demo} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Demo
                        </a>
                      </Button>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="mt-4 text-xs text-muted-foreground/60">
              {clickCount > 0 && `Clicked ${clickCount} times • `}
              {isExpanded ? 'Click to collapse' : 'Click to expand'}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
