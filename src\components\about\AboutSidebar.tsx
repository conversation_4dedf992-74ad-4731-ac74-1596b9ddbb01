'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';

const passions = [
  { id: 'ai', emoji: '🤖', name: 'Artificial Intelligence', color: 'text-blue-400' },
  { id: 'python', emoji: '🐍', name: 'Python', color: 'text-green-400' },
  { id: 'figma', emoji: '🎨', name: 'Figma Design', color: 'text-purple-400' },
  { id: 'painting', emoji: '🖌️', name: 'Digital Painting', color: 'text-pink-400' },
  { id: 'ux', emoji: '✨', name: 'UX Design', color: 'text-orange-400' },
  { id: 'mario', emoji: '🍄', name: '<PERSON>', color: 'text-red-400' },
  { id: 'coffee', emoji: '☕', name: 'Coffee', color: 'text-yellow-400' },
  { id: 'music', emoji: '🎵', name: 'Lo-fi Music', color: 'text-cyan-400' }
];

const techCharacter = {
  idle: '🧑‍💻',
  working: '⚡',
  thinking: '🤔',
  celebrating: '🎉',
  debugging: '🐛'
};

export function AboutSidebar() {
  const [selectedPassion, setSelectedPassion] = useState<string | null>(null);
  const [characterState, setCharacterState] = useState<keyof typeof techCharacter>('idle');
  const [discoveredPassions, setDiscoveredPassions] = useState<string[]>([]);
  const [showSecretMessage, setShowSecretMessage] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Character state changes
  useEffect(() => {
    const states: (keyof typeof techCharacter)[] = ['idle', 'working', 'thinking'];
    let counter = 0;
    const interval = setInterval(() => {
      setCharacterState(states[counter % states.length]);
      counter++;
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handlePassionClick = (passionId: string) => {
    setSelectedPassion(selectedPassion === passionId ? null : passionId);
    
    if (!discoveredPassions.includes(passionId)) {
      setDiscoveredPassions(prev => [...prev, passionId]);
      setCharacterState('celebrating');
      setTimeout(() => setCharacterState('idle'), 2000);
    }

    // Secret unlock when all passions discovered
    if (discoveredPassions.length === passions.length - 1 && !discoveredPassions.includes(passionId)) {
      setTimeout(() => setShowSecretMessage(true), 1000);
    }
  };

  const handleCharacterClick = () => {
    setCharacterState('debugging');
    setTimeout(() => setCharacterState('working'), 1000);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  return (
    <div className="space-y-6" onMouseMove={handleMouseMove}>
      {/* Animated Tech Character */}
      <Card className="bg-gradient-to-br from-primary/10 to-purple-600/10 border-primary/20 overflow-hidden">
        <CardContent className="p-6 text-center relative">
          <motion.div
            className="text-8xl mb-4 cursor-pointer select-none"
            animate={{
              scale: characterState === 'celebrating' ? [1, 1.2, 1] : 1,
              rotate: characterState === 'working' ? [0, 5, -5, 0] : 0
            }}
            transition={{ duration: 0.5 }}
            onClick={handleCharacterClick}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {techCharacter[characterState]}
          </motion.div>
          
          <motion.h3 
            className="text-xl font-bold mb-2"
            animate={{ color: characterState === 'celebrating' ? '#10b981' : '#ffffff' }}
          >
            Ghazal the Tech Wizard
          </motion.h3>
          
          <motion.p 
            className="text-sm text-muted-foreground"
            key={characterState}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            {characterState === 'idle' && 'Ready for the next challenge...'}
            {characterState === 'working' && 'Coding something amazing...'}
            {characterState === 'thinking' && 'Solving complex problems...'}
            {characterState === 'celebrating' && 'Another passion discovered!'}
            {characterState === 'debugging' && 'Hunting down those bugs...'}
          </motion.p>

          {/* Floating code particles around character */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {['{}', '()', '[]', '<>', '//'].map((symbol, i) => (
              <motion.div
                key={i}
                className="absolute text-primary/20 font-mono text-sm"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${20 + i * 10}%`,
                }}
                animate={{
                  y: [0, -10, 0],
                  opacity: [0.2, 0.5, 0.2],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: 3 + i,
                  repeat: Infinity,
                  delay: i * 0.5
                }}
              >
                {symbol}
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Interactive Passions List */}
      <Card className="bg-gradient-to-br from-secondary/50 to-accent/50 border-secondary/20">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <motion.span
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mr-2"
            >
              ⚡
            </motion.span>
            My Passions
          </h3>
          
          <div className="grid grid-cols-2 gap-3">
            {passions.map((passion, index) => (
              <motion.div
                key={passion.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  p-3 rounded-lg cursor-pointer transition-all duration-300
                  ${discoveredPassions.includes(passion.id) 
                    ? 'bg-primary/20 border border-primary/40' 
                    : 'bg-muted/50 hover:bg-muted/80'
                  }
                  ${selectedPassion === passion.id ? 'ring-2 ring-primary ring-opacity-75' : ''}
                `}
                onClick={() => handlePassionClick(passion.id)}
              >
                <div className="flex items-center space-x-2">
                  <motion.span
                    className="text-lg"
                    animate={selectedPassion === passion.id ? {
                      scale: [1, 1.3, 1],
                      rotate: [0, 360, 0]
                    } : {}}
                    transition={{ duration: 0.5 }}
                  >
                    {passion.emoji}
                  </motion.span>
                  <span className={`text-sm font-medium ${passion.color}`}>
                    {passion.name}
                  </span>
                </div>
                
                {/* Discovery indicator */}
                {discoveredPassions.includes(passion.id) && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="mt-1 text-xs text-primary/80"
                  >
                    ✓ Discovered!
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Progress indicator */}
          <div className="mt-4 pt-4 border-t border-border">
            <div className="flex justify-between text-xs text-muted-foreground mb-2">
              <span>Passions Discovered</span>
              <span>{discoveredPassions.length}/{passions.length}</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-primary to-purple-600"
                initial={{ width: 0 }}
                animate={{ width: `${(discoveredPassions.length / passions.length) * 100}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Secret Message */}
      <AnimatePresence>
        {showSecretMessage && (
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.8 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
            onClick={() => setShowSecretMessage(false)}
          >
            <Card className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-none shadow-2xl max-w-md mx-4">
              <CardContent className="p-8 text-center">
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 360, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-6xl mb-4"
                >
                  🎉
                </motion.div>
                <h3 className="text-2xl font-bold mb-4">
                  All Passions Discovered!
                </h3>
                <p className="mb-4">
                  You've unlocked the secret: I'm not just a tech person, I'm a creative soul who believes technology should be beautiful, intuitive, and fun!
                </p>
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-sm opacity-80"
                >
                  Click anywhere to continue exploring...
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Fun Facts Card */}
      <Card className="bg-gradient-to-br from-green-500/10 to-blue-500/10 border-green-500/20">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <motion.span
              animate={{ y: [0, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="mr-2"
            >
              💡
            </motion.span>
            Fun Facts
          </h3>
          
          <div className="space-y-3 text-sm">
            <motion.div
              whileHover={{ x: 5 }}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <span>🌙</span>
              <span>I do my best coding between 10 PM and 2 AM</span>
            </motion.div>
            
            <motion.div
              whileHover={{ x: 5 }}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <span>🎮</span>
              <span>I've beaten Mario Kart on 200cc difficulty</span>
            </motion.div>
            
            <motion.div
              whileHover={{ x: 5 }}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <span>🎨</span>
              <span>I design UI mockups while listening to lo-fi</span>
            </motion.div>
            
            <motion.div
              whileHover={{ x: 5 }}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <span>☕</span>
              <span>My code quality is directly proportional to coffee intake</span>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* Mouse follower effect */}
      <motion.div
        className="fixed pointer-events-none z-30 w-4 h-4 bg-primary/30 rounded-full"
        animate={{
          x: mousePosition.x - 8,
          y: mousePosition.y - 8,
        }}
        transition={{ type: "spring", stiffness: 500, damping: 28 }}
      />
    </div>
  );
}
