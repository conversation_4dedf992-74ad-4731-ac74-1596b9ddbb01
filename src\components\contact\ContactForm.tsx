'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Send, Sparkles, Zap } from 'lucide-react';

export function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [charCount, setCharCount] = useState(0);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (field === 'message') {
      setCharCount(value.length);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setSubmitted(true);
    setShowSuccess(true);
    
    // Reset form after success animation
    setTimeout(() => {
      setFormData({ name: '', email: '', message: '' });
      setCharCount(0);
      setSubmitted(false);
      setShowSuccess(false);
    }, 3000);
  };

  const isFormValid = formData.name && formData.email && formData.message;

  return (
    <Card className="bg-gradient-to-br from-primary/5 to-purple-600/5 border-primary/20 overflow-hidden">
      <CardContent className="p-8 relative">
        {/* Animated background particles */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {Array.from({ length: 10 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-primary/20 rounded-full"
              style={{
                left: `${(i * 123) % 100}%`,
                top: `${(i * 456) % 100}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.3
              }}
            />
          ))}
        </div>

        <div className="relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-4xl mb-4"
            >
              📬
            </motion.div>
            <h2 className="text-2xl font-bold mb-2">Send Me a Message</h2>
            <p className="text-muted-foreground">
              Ready for job offers, collaborations, or just "you're awesome" messages!
            </p>
          </motion.div>

          <AnimatePresence mode="wait">
            {!submitted ? (
              <motion.form
                key="form"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onSubmit={handleSubmit}
                className="space-y-6"
              >
                {/* Name Field */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  <motion.label
                    className={`absolute left-3 transition-all duration-300 pointer-events-none ${
                      focusedField === 'name' || formData.name
                        ? 'top-2 text-xs text-primary'
                        : 'top-4 text-sm text-muted-foreground'
                    }`}
                    animate={{
                      y: focusedField === 'name' || formData.name ? -8 : 0,
                      scale: focusedField === 'name' || formData.name ? 0.85 : 1
                    }}
                  >
                    Your Name
                  </motion.label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    onFocus={() => setFocusedField('name')}
                    onBlur={() => setFocusedField(null)}
                    className={`
                      w-full px-3 py-4 pt-6 rounded-lg border-2 bg-background/50 backdrop-blur-sm
                      transition-all duration-300 focus:outline-none
                      ${focusedField === 'name' 
                        ? 'border-primary shadow-lg shadow-primary/20' 
                        : 'border-muted hover:border-primary/50'
                      }
                    `}
                  />
                  {focusedField === 'name' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute right-3 top-4 text-primary"
                    >
                      <Sparkles className="w-4 h-4" />
                    </motion.div>
                  )}
                </motion.div>

                {/* Email Field */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  <motion.label
                    className={`absolute left-3 transition-all duration-300 pointer-events-none ${
                      focusedField === 'email' || formData.email
                        ? 'top-2 text-xs text-primary'
                        : 'top-4 text-sm text-muted-foreground'
                    }`}
                    animate={{
                      y: focusedField === 'email' || formData.email ? -8 : 0,
                      scale: focusedField === 'email' || formData.email ? 0.85 : 1
                    }}
                  >
                    Your Email
                  </motion.label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    onFocus={() => setFocusedField('email')}
                    onBlur={() => setFocusedField(null)}
                    className={`
                      w-full px-3 py-4 pt-6 rounded-lg border-2 bg-background/50 backdrop-blur-sm
                      transition-all duration-300 focus:outline-none
                      ${focusedField === 'email' 
                        ? 'border-primary shadow-lg shadow-primary/20' 
                        : 'border-muted hover:border-primary/50'
                      }
                    `}
                  />
                  {focusedField === 'email' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute right-3 top-4 text-primary"
                    >
                      <Zap className="w-4 h-4" />
                    </motion.div>
                  )}
                </motion.div>

                {/* Message Field */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  <motion.label
                    className={`absolute left-3 transition-all duration-300 pointer-events-none ${
                      focusedField === 'message' || formData.message
                        ? 'top-2 text-xs text-primary'
                        : 'top-4 text-sm text-muted-foreground'
                    }`}
                    animate={{
                      y: focusedField === 'message' || formData.message ? -8 : 0,
                      scale: focusedField === 'message' || formData.message ? 0.85 : 1
                    }}
                  >
                    Your Message
                  </motion.label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    onFocus={() => setFocusedField('message')}
                    onBlur={() => setFocusedField(null)}
                    rows={5}
                    maxLength={500}
                    className={`
                      w-full px-3 py-4 pt-6 rounded-lg border-2 bg-background/50 backdrop-blur-sm
                      transition-all duration-300 focus:outline-none resize-none
                      ${focusedField === 'message' 
                        ? 'border-primary shadow-lg shadow-primary/20' 
                        : 'border-muted hover:border-primary/50'
                      }
                    `}
                  />
                  <div className="absolute bottom-2 right-3 text-xs text-muted-foreground">
                    {charCount}/500
                  </div>
                  {focusedField === 'message' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute right-3 top-4 text-primary"
                    >
                      <Send className="w-4 h-4" />
                    </motion.div>
                  )}
                </motion.div>

                {/* Submit Button */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    disabled={!isFormValid || isSubmitting}
                    className={`
                      w-full py-4 text-lg font-semibold rounded-lg transition-all duration-300
                      ${isFormValid 
                        ? 'bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 shadow-lg hover:shadow-xl' 
                        : 'bg-muted text-muted-foreground cursor-not-allowed'
                      }
                    `}
                  >
                    <AnimatePresence mode="wait">
                      {isSubmitting ? (
                        <motion.div
                          key="submitting"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="flex items-center space-x-2"
                        >
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          >
                            ⚡
                          </motion.div>
                          <span>Sending Message...</span>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="send"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="flex items-center space-x-2"
                        >
                          <Send className="w-5 h-5" />
                          <span>Send Message</span>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Button>
                </motion.div>
              </motion.form>
            ) : (
              <motion.div
                key="success"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="text-center py-12"
              >
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 360, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-6xl mb-6"
                >
                  ✅
                </motion.div>
                <h3 className="text-2xl font-bold text-green-400 mb-4">
                  Message Sent Successfully!
                </h3>
                <p className="text-muted-foreground mb-6">
                  Thanks for reaching out! I'll get back to you within 24 hours.
                </p>
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-sm text-primary"
                >
                  Resetting form...
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Success particles */}
        <AnimatePresence>
          {showSuccess && (
            <div className="absolute inset-0 pointer-events-none">
              {Array.from({ length: 20 }).map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ 
                    scale: 0, 
                    x: '50%', 
                    y: '50%', 
                    opacity: 1 
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    x: `${50 + (((i * 123) % 100) - 50)}%`,
                    y: `${50 + (((i * 456) % 100) - 50)}%`,
                    opacity: [1, 1, 0]
                  }}
                  transition={{ 
                    duration: 2,
                    delay: i * 0.1,
                    ease: "easeOut"
                  }}
                  className="absolute text-2xl"
                >
                  {['🎉', '✨', '🌟', '💫'][i % 4]}
                </motion.div>
              ))}
            </div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}
